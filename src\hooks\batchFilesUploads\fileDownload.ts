import { useQuery } from "@tanstack/react-query";
import useDataService from "@/services/useDataService";
import { DOWNLOADFILE_URL } from "@/constants/urls";

interface MetaDownloadResponse {
  success: boolean;
  code: number;
  message: string;
  result: any; // Replace with actual type if known
}

export const useGetMetaDownload = (
  batchId: number,
  enabled: boolean = true
) => {
  return useQuery<MetaDownloadResponse>({
    queryKey: ["meta-download", batchId],
    queryFn: async () => {
      return await useDataService.getService(`${DOWNLOADFILE_URL}/${batchId}`);
    },
    enabled,
    staleTime: 0,
  });
};
