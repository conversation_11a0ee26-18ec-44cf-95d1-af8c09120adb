// services/hooks/useImportCorrectedData.ts
import { useMutation } from '@tanstack/react-query';
import useDataService from '../../services/useDataService';
import { IMPORT_CORRECTED_DATA } from "@/constants/urls";

interface ImportCorrectedResponse {
  success: boolean;
  result: {
    data: any; // Can replace with proper shape if needed
  };
  message: string;
  code: number;
}

export const useImportCorrectedData = () => {
  return useMutation<ImportCorrectedResponse, Error, number>({
    mutationFn: async (batchId: number) => {
      const url = `${IMPORT_CORRECTED_DATA}/${batchId}`;
      return await useDataService.postService(url, {});
    },
  });
};
