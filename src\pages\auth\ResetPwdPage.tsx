import { Controller, useForm } from "react-hook-form";
import { <PERSON>, useNavigate } from "react-router-dom";
import { <PERSON><PERSON>eft, Eye, EyeOff } from "lucide-react";
import { Input } from "@/components/ui/input";
import { useToast } from "@/hooks/useToast";
import <PERSON><PERSON>L<PERSON> from "@/components/common/FlashanaLogo";
import { ToastContainer } from "@/components/common/Toast";
import * as yup from "yup";
import { yupResolver } from "@hookform/resolvers/yup";
import { useState } from "react";
import {
  InputOTP,
  InputOTPGroup,
  InputOTPSlot,
} from "@/components/ui/input-otp";
import { usePostResetPassword } from "@/hooks/auth/resetPwdHooks";
import { useForgotPasswordStore } from "@/store/forgotPwdStore";

const schema = yup.object({
  otp: yup
    .string()
    .required("OTP is required")
    .length(6, "OTP must be exactly 6 digits"),
  newPassword: yup.string().required("New password is required")
    .matches(
      /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[^A-Za-z0-9])(?!.*\s).{8,}$/,
      "Invalid password (must include uppercase, lowercase, number, special character)"
    ),
  confirmPassword: yup
    .string()
    .oneOf([yup.ref("newPassword")], "Passwords do not match")
    .required("Confirm password is required"),
});

export interface ResetPwdFormData {
  otp: string;
  newPassword: string;
  confirmPassword: string;
}

export default function ResetPwdPage() {
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const { toasts, removeToast, success, error } = useToast();
  const navigate = useNavigate();
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);


  const {
    register,
    handleSubmit,
    formState: { errors },
    control,
  } = useForm<ResetPwdFormData>({
    resolver: yupResolver(schema),
  });

  const onPostSuccess = (data: any) => {
    setIsLoading(false);
    success("Success", `${data?.message || "Password reset successfully."}`);

    // Clear email from zustand
    useForgotPasswordStore.getState().clearEmail();

    setTimeout(() => {
      navigate("/auth/login");
    }, 3000);
  };

  const onPostError = (errorRes: any) => {
    setIsLoading(false);
    const errorInfo =
      errorRes?.error?.message || "Something went wrong. Please try again.";
    error("Error", `${errorInfo}`);
  };

  const { postMutate } = usePostResetPassword(onPostSuccess, onPostError);

  const onSubmit = async (data: ResetPwdFormData) => {
    setIsLoading(true);

    try {
      // Fetch email from zustand
      const email = useForgotPasswordStore.getState().email;

      const dataPayload: any = {
        email: email || "",
        confirmation_code: data?.otp || "",
        new_password: data?.newPassword || "",
      };

      postMutate(dataPayload);
    } catch (error) {
      console.error("Reset Password request failed:", error);
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-white">
      <div className="w-full max-w-md flex flex-col items-center px-4">
        {/* Logo */}
        <div className="mb-8 flex justify-center">
          <FlashanaLogo variant="full" size="large" animated={false} />
        </div>
        {/* Header */}
        <h1 className="text-2xl text-primary font-semibold text-center mb-4">
          Forgot Password
        </h1>
        <p className="text-zinc-700 text-center text-sm mb-8">
          Please enter your new password.
        </p>

        <form className="w-full space-y-4" onSubmit={handleSubmit(onSubmit)}>
          {/* OTP */}
          <div className="text-left">
            <label
              htmlFor="otp"
              className="block text-sm font-medium text-gray-700 mb-1"
            >
              OTP<span className="text-red-500">*</span>
            </label>
            <Controller
              control={control}
              name="otp"
              rules={{
                required: "OTP is required",
                minLength: { value: 6, message: "OTP must be 6 digits" },
                maxLength: { value: 6, message: "OTP must be 6 digits" },
              }}
              render={({ field }) => (
                <InputOTP
                  maxLength={6}
                  value={field.value || ""}
                  onChange={field.onChange}
                  onBlur={field.onBlur}
                  className="w-full"
                >
                  <InputOTPGroup className="w-full">
                    {[...Array(6)].map((_, i) => (
                      <InputOTPSlot key={i} index={i} />
                    ))}
                  </InputOTPGroup>
                </InputOTP>
              )}
            />
            {errors.otp && (
              <p className="text-sm text-red-600 mt-1 text-left">
                {errors.otp.message}
              </p>
            )}
          </div>

          {/* New Password */}
          <div className="text-left">
            <label
              htmlFor="newPassword"
              className="block text-sm font-medium text-gray-700 mb-1"
            >
              New Password<span className="text-red-500">*</span>
            </label>
            <div className="relative">
              <Input
                id="newPassword"
                type={showPassword ? "text" : "password"}
                {...register("newPassword")}
                onInput={e => {
                  const target = e.target as HTMLInputElement;
                  target.value = target.value.replace(/\s/g, "");
                }}

                className={errors.newPassword ? "border-destructive pr-8" : "pr-8"}
              />
              <button
                type="button"
                onClick={() => setShowPassword(!showPassword)}
                className="absolute right-2 top-1/2 -translate-y-1/2 text-zinc-500"
                tabIndex={-1}
              >
                {showPassword ? <EyeOff size={20} /> : <Eye size={20} />}
              </button>
            </div>
            {errors.newPassword && (
              <p className="text-sm text-red-600 mt-1">
                {errors.newPassword.message}
              </p>
            )}
          </div>

          {/* Confirm Password */}
          <div className="text-left">
            <label
              htmlFor="confirmPassword"
              className="block text-sm font-medium text-gray-700 mb-1"
            >
              Confirm Password<span className="text-red-500">*</span>
            </label>
            <div className="relative">
              <Input
                id="confirmPassword"
                type={showConfirmPassword ? "text" : "password"}
                {...register("confirmPassword")}
                className={errors.confirmPassword ? "border-destructive pr-8" : "pr-8"}
                onInput={e => {
                  const target = e.target as HTMLInputElement;
                  target.value = target.value.replace(/\s/g, "");
                }}

              />
              <button
                type="button"
                onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                className="absolute right-2 top-1/2 -translate-y-1/2 text-zinc-500"
                tabIndex={-1}
              >
                {showConfirmPassword ? <EyeOff size={20} /> : <Eye size={20} />}
              </button>
            </div>
            {errors.confirmPassword && (
              <p className="text-sm text-red-600 mt-1">
                {errors.confirmPassword.message}
              </p>
            )}
          </div>

          {/* Submit Button */}
          <button
            type="submit"
            disabled={isLoading}
            className="w-full bg-[#22423F] text-white py-3 px-4 text-base font-semibold rounded-md transition-all"
          >
            {isLoading ? "Submitting..." : "Submit"}
          </button>
        </form>

        {/* Back to login */}
        <div className="text-sm text-center text-gray-700 mt-8">
          <Link
            to="/auth/login"
            className="flex items-center justify-center hover:underline"
          >
            <ArrowLeft className="mr-1 w-4 h-4" />
            Back to login
          </Link>
        </div>

        {/* Toasts */}
        <ToastContainer toasts={toasts} onClose={removeToast} />
      </div>
    </div>
  );
}
