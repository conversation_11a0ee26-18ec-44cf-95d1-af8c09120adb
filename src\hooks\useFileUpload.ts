import { useState } from 'react';
import { useMutation } from '@tanstack/react-query';
import { useSnackbar } from 'notistack';
import { api } from '../services/api';
import { FileUploadResponse } from '../types';

export interface BulkFileUploadRequest {
  session_id: string;
  payroll_file: File;
  attendance_file: File;
  government_file: File;
}

export const useFileUpload = () => {
  const [uploadProgress, setUploadProgress] = useState<number>(0);
  const { enqueueSnackbar } = useSnackbar();

  const uploadMutation = useMutation<FileUploadResponse, Error, BulkFileUploadRequest>({
    mutationFn: async (data) => {
      const formData = new FormData();
      formData.append('payroll_file', data.payroll_file);
      formData.append('attendance_file', data.attendance_file);
      formData.append('government_file', data.government_file);

      const response = await api.uploadFiles(data.session_id, formData, (progressEvent) => {
        const progress = progressEvent.total
          ? Math.round((progressEvent.loaded * 100) / progressEvent.total)
          : 0;
        setUploadProgress(progress);
      });

      return response;
    },
    onSuccess: () => {
      enqueueSnackbar('Successfully uploaded all files', {
        variant: 'success',
      });
      setUploadProgress(0);
    },
    onError: (error) => {
      enqueueSnackbar(`Failed to upload files: ${error.message}`, {
        variant: 'error',
      });
      setUploadProgress(0);
    },
  });

  return {
    uploadFiles: uploadMutation.mutate,
    uploadFilesAsync: uploadMutation.mutateAsync,
    isUploading: uploadMutation.isPending,
    uploadProgress,
  };
};