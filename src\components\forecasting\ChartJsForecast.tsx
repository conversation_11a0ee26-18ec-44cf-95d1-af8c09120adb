import React, { useRef, useState } from "react";
import {
  Chart as ChartJS,
  LineElement,
  PointElement,
  CategoryScale,
  LinearScale,
  Title,
  Tooltip,
  Legend,
  Filler,
} from "chart.js";
import { Line } from "react-chartjs-2";
import zoomPlugin from "chartjs-plugin-zoom";
import annotationPlugin from "chartjs-plugin-annotation";
import { Download, Eye, EyeOff, RotateCcw, ZoomIn, ZoomOut } from "lucide-react";

// Register Chart.js components & plugins
ChartJS.register(
  LineElement,
  PointElement,
  CategoryScale,
  LinearScale,
  Title,
  Tooltip,
  Legend,
  Filler,
  zoomPlugin,
  annotationPlugin
);

const legendColors = {
  historical: "#2b524f",
  actuals: "#3b82f6",
  forecast: "#16a34a",
  confidence: "rgba(106, 109, 114, 0.2)",
};

const BUTTONS = [
  { key: "historical", label: "Historical", color: legendColors.historical },
  { key: "actuals", label: "Actuals", color: legendColors.actuals },
  { key: "forecast", label: "Forecast", color: legendColors.forecast },
  { key: "confidence", label: "Confidence", color: legendColors.confidence },
];

// Util: format currency for tooltip or axis
const formatCurrency = (v) =>
  v == null
    ? ""
    : v.toLocaleString("en-CA", { style: "decimal", maximumFractionDigits: 0 });

export default function ChartJsForecast({
  historicalData,
  actualsData,
  forecastData,
  isLoading = false,
}) {
  // Find cutoff date and today's date
  const historicalCutoffDate = historicalData.length
    ? historicalData[historicalData.length - 1].date
    : "";
  const today = new Date().toISOString().slice(0, 10);

  // Merge x-axis (labels) for all datasets (assume all date strings are unique and ordered)
  const allDates = [
    ...historicalData.map((d) => d.date),
    ...actualsData.map((d) => d.date),
    ...forecastData.map((d) => d.date),
  ];
  // De-duplicate, in order
  const labels = [...new Set(allDates)];

  // Align datasets to all labels (filling nulls)
  const alignData = (dataset) =>
    labels.map((date) => {
      const found = dataset.find((d) => d.date === date);
      return found ? found.value : null;
    });

  const historicalLine = alignData(historicalData);
  const actualsLine = alignData(actualsData);
  const forecastLine = alignData(forecastData);
  // For confidence (only forecastData), build upper/lower arrays
  const confidenceUpper = labels.map((date) => {
    const d = forecastData.find((d) => d.date === date);
    return d && d.confidence_upper ? d.confidence_upper : null;
  });
  const confidenceLower = labels.map((date) => {
    const d = forecastData.find((d) => d.date === date);
    return d && d.confidence_lower ? d.confidence_lower : null;
  });

  // Toggle visibility
  const [visible, setVisible] = useState({
    historical: true,
    actuals: true,
    forecast: true,
    confidence: true,
  });

  // Ref for chart instance (for reset zoom)
  const chartRef = useRef();

  // Data structure for Chart.js
  const chartData = {
    labels,
    datasets: [
      // Historical
      {
        label: "Historical Data",
        data: visible.historical ? historicalLine : labels.map(() => null),
        borderColor: legendColors.historical,
        pointBackgroundColor: legendColors.historical,
        fill: false,
        tension: 0.3,
        borderWidth: 3,
        pointRadius: 4,
        spanGaps: true,
        order: 1,
      },
      // Actuals
      {
        label: "Actuals Data",
        data: visible.actuals ? actualsLine : labels.map(() => null),
        borderColor: legendColors.actuals,
        pointBackgroundColor: legendColors.actuals,
        fill: false,
        borderWidth: 3,
        pointRadius: 4,
        spanGaps: true,
        order: 1,
      },
      // Forecast (ONLY when visible.forecast is true)
      ...(visible.forecast
        ? [
          {
            label: "Forecast Data",
            data: forecastLine,
            borderColor: legendColors.forecast,
            borderDash: [8, 4],
            pointBackgroundColor: legendColors.forecast,
            fill: false,
            borderWidth: 3,
            pointRadius: 3,
            spanGaps: true,
            order: 2,
          },
        ]
        : []),
      // Confidence upper/lower (ONLY when visible.confidence is true)
      ...(visible.confidence
        ? [
          // Lower confidence bound (must come before upper!)
          {
            label: "Confidence Interval Lower",
            data: confidenceLower, // your calculated lower array
            borderWidth: 0,
            backgroundColor: "rgba(0,0,0,0)",
            fill: false,
            pointRadius: 0,
            order: 0,
          },
          // Upper confidence bound — this "fills to previous" lower dataset
          {
            label: "Confidence Interval Upper",
            data: confidenceUpper, // your calculated upper array
            borderWidth: 0,
            backgroundColor: "rgba(156,163,175,0.18)",
            fill: "-1", // fill to previous dataset (lower)
            pointRadius: 0,
            order: 0,
          },
        ]
        : []),
    ],
  };

  // Chart.js options including plugins
  const options = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: { display: false },
      tooltip: {
        callbacks: {
          label: function (context: any) {
            return (
              (context.dataset.label || "") +
              ": " +
              formatCurrency(context.parsed.y)
            );
          },
        },
      },
      title: { display: false },
      annotation: {
        annotations: {
          historicalCutoff: {
            type: "line" as const, // explicitly as const for string literal
            xMin: labels.indexOf(historicalCutoffDate),
            xMax: labels.indexOf(historicalCutoffDate),
            borderColor: "black",
            borderWidth: 2,
            borderDash: [6, 4],
            label: {
              content: "Historical Data Cutoff",
              // enabled: false,
              display: true,
              position: "end" as const,
              backgroundColor: "#fff",
              color: "#000",
            },
          },
          today: {
            type: "line" as const,
            xMin: labels.indexOf(today),
            xMax: labels.indexOf(today),
            borderColor: "#AB732B",
            borderWidth: 2,
            borderDash: [6, 4],
            label: {
              content: "Today",
              enabled: false,
              display: true,
              position: "end" as const,
              backgroundColor: "#fff",
              color: "#AB732B",
            },
          },
        },
      },
      zoom: {
        pan: { enabled: false, mode: "xy" as const },
        zoom: {
          wheel: { enabled: false },
          pinch: { enabled: false },
          mode: "xy" as const,
        },
      },
    },
    interaction: {
      mode: "nearest" as const, intersect: false
    },
    scales: {
      x: {
        type: "category" as const,
        title: { display: false },
        ticks: {
          callback: function (value, index) {
            const dt = labels[index];
            if (!dt) return "";
            return new Date(dt).toLocaleDateString("en-US", {
              month: "short",
              day: "numeric",
            });
          },
          maxRotation: 0,
          autoSkip: true,
          maxTicksLimit: 10,
        },
        grid: { display: true },
      },
      y: {
        beginAtZero: false,
        title: { display: false },
        ticks: { callback: formatCurrency },
        grid: { display: true },
      },
    },
  };

  const whiteBackground = {
    id: "white-background",
    beforeDraw: (chart) => {
      const ctx = chart.ctx;
      ctx.save();
      ctx.globalCompositeOperation = "destination-over";
      ctx.fillStyle = "#fff"; // White background
      ctx.fillRect(0, 0, chart.width, chart.height);
      ctx.restore();
    },
  };

  const zoomIn = () => {
    const chart = chartRef.current;
    if (chart) {
      // Zoom in by a factor (e.g., 1.2)
      // @ts-expect-error zoom method exists at runtime
      chart.zoom(1.2);
    }
  };

  const zoomOut = () => {
    const chart = chartRef.current;
    if (chart) {
      // Zoom out by factor 1/1.2
      // @ts-expect-error zoom method exists at runtime
      chart.zoom(1 / 1.2);
    }
  };

  if (isLoading) {
    return <div>Loading chart data...</div>;
  }

  return (
    <div style={{ position: "relative", width: "100%", minHeight: 440 }} >
      {/* Top legend and toggles */}
      < div
        style={{
          display: "flex",
          justifyContent: "flex-end",
          gap: "8px",
          marginBottom: 10,
        }
        }
      >
        {
          BUTTONS.map((btn) => (
            <>
              <button
                key={btn.key}
                onClick={() =>
                  setVisible((v) => ({ ...v, [btn.key]: !v[btn.key] }))
                }
                style={{
                  display: "flex",
                  alignItems: "center",
                  gap: 4,
                  padding: "6px 16px",
                  borderRadius: 6,
                  border: "1px solid #e4e4e7",
                  background: visible[btn.key] ? btn.color : "#fff",
                  color: visible[btn.key]
                    ? btn.key == "confidence"
                      ? "#374151"
                      : "#fff"
                    : "#374151",
                  fontSize: "0.85rem",
                  fontWeight: 600,
                  cursor: "pointer",
                }}
              >
                {visible[btn.key] ? <Eye size={12} /> : <EyeOff size={12} />}
                {btn.label}
              </button>
            </>
          ))
        }
      </div >
      <div style={{ height: 400, width: "100%" }}>
        <Line
          ref={chartRef}
          data={chartData}
          options={options}
          plugins={[whiteBackground]}
        />
      </div>
      {/* Controls: Zoom/Export */}
      <div
        style={{
          display: "flex",
          justifyContent: "flex-end",
          gap: "0.25rem",
          marginTop: 8,
        }}
      >
        {/* ===== Zoom In Button ======*/}
        {/* <button
          onClick={() => {
            const chart = chartRef.current;
            if (chart) {
              // @ts-expect-error
              chart.zoom(1.2);

            }
          }}
          style={{
            padding: "0.5rem",
            borderRadius: "0.375rem",
            border: "1px solid #e4e4e7",
            backgroundColor: "white",
            color: "#374151",
            cursor: "pointer",
            transition: "all 0.2s",
          }}
          title="Zoom In"
        >
          <ZoomIn size={14} />
        </button> */}

        {/* ===== Zoom Out Button ====== */}
        {/* <button
          onClick={() => {
            const chart = chartRef.current;
            if (chart) {
              // @ts-expect-error
              chart.zoom(1 / 1.2);
            }
          }}
          style={{
            padding: "0.5rem",
            borderRadius: "0.375rem",
            border: "1px solid #e4e4e7",
            backgroundColor: "white",
            color: "#374151",
            cursor: "pointer",
            transition: "all 0.2s",
          }}
          title="Zoom Out"
        >
          <ZoomOut size={14} />
        </button> */}

        {/* ===== Reset Zoom Button ====== */}

        {/* 
        <button
          onClick={() => {
            const chart = chartRef.current;
            if (chart) {
              // @ts-expect-error
              chart.resetZoom();
            }
          }}
          style={{
            padding: "0.5rem",
            borderRadius: "0.375rem",
            border: "1px solid #e4e4e7",
            backgroundColor: "white",
            color: "#374151",
            cursor: "pointer",
            transition: "all 0.2s",
          }}
          title="Reset"
        >
          <RotateCcw size={14} />
        </button> */}

        {/* === Download Button ==== */}
        <button
          onClick={() => {
            const chart = chartRef.current;
            if (chart) {
              // @ts-expect-error
              const url = chart.toBase64Image();
              const link = document.createElement("a");
              link.download = "forecast-chart.png";
              link.href = url;
              link.click();
            }
          }}
          style={{
            padding: "0.5rem",
            borderRadius: "0.375rem",
            border: "1px solid #e4e4e7",
            backgroundColor: "white",
            color: "#374151",
            cursor: "pointer",
            transition: "all 0.2s",
          }}
          title="Export Chart"
        >
          <Download size={14} />
        </button>
      </div>

      {/* Legend */}
      <div
        style={{
          display: "flex",
          gap: "1rem",
          fontSize: "0.82rem",
          justifyContent: "center",
          color: "#71717a",
          marginTop: 12,
        }}
      >
        <span style={{ display: "flex", alignItems: "center", gap: 4 }}>
          <span style={{ width: 14, height: 4, backgroundColor: "#2b524f" }} />
          Historical Data
        </span>
        <span style={{ display: "flex", alignItems: "center", gap: 4 }}>
          <span style={{ width: 14, height: 4, backgroundColor: "#3b82f6" }} />
          Actuals Data
        </span>
        <span style={{ display: "flex", alignItems: "center", gap: 4 }}>
          <span
            style={{
              width: 14,
              height: 4,
              backgroundColor: "#16a34a",
              borderTop: "2px dashed #16a34a",
            }}
          />
          Forecast Data
        </span>
        <span style={{ display: "flex", alignItems: "center", gap: 4 }}>
          <span
            style={{
              width: 14,
              height: 8,
              backgroundColor: "rgba(156,163,175,0.2)",
            }}
          />
          Confidence Interval
        </span>
      </div>
    </div >
  );
}
