import React, { useState } from "react";
import { useAuthStore } from "../../store/authStore";
import {
  User,
  Camera,
  Mail,
  Shield,
  Save,
  X,
  Edit2,
  AlertCircle,
} from "lucide-react";
import { useNavigate } from "react-router-dom";

interface ProfileFormData {
  name: string;
  email: string;
  role: string;
  avatar?: string;
}

export default function ProfilePage() {
  const { user, login } = useAuthStore();
  const navigate = useNavigate();
  const [isEditing, setIsEditing] = useState(false);
  const [formData, setFormData] = useState<ProfileFormData>({
    name: user?.username || "",
    email: user?.email || "",
    role: user?.role || "",
    avatar: user?.avatar || "",
  });
  const [errors, setErrors] = useState<Partial<ProfileFormData>>({});
  const [successMessage, setSuccessMessage] = useState("");

  const validateForm = (): boolean => {
    const newErrors: Partial<ProfileFormData> = {};

    if (!formData.name.trim()) {
      newErrors.name = "Name is required";
    } else if (formData.name.trim().length < 2) {
      newErrors.name = "Name must be at least 2 characters";
    }

    if (!formData.email.trim()) {
      newErrors.email = "Email is required";
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      newErrors.email = "Invalid email format";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    // Update user in auth store
    if (user) {
      const updatedUser = {
        ...user,
        ...formData,
      };
      login(updatedUser);
      setSuccessMessage("Profile updated successfully!");
      setIsEditing(false);

      // Clear success message after 3 seconds
      setTimeout(() => {
        setSuccessMessage("");
      }, 3000);
    }
  };

  const handleCancel = () => {
    setFormData({
      name: user?.username || "",
      email: user?.email || "",
      role: user?.role || "",
      avatar: user?.avatar || "",
    });
    setErrors({});
    setIsEditing(false);
  };

  const getInitials = (name: string) => {
    return name
      .split(" ")
      .map((n) => n[0])
      .join("")
      .toUpperCase()
      .slice(0, 2);
  };

  if (!user) {
    navigate("/auth/login");
    return null;
  }

  return (
    <div className="p-6">
      <div className="max-w-4xl mx-auto">
        {/* Header */}
        <div className="mb-6">
          <h1 className="text-2xl font-semibold flashana-body-text">
            Profile Settings
          </h1>
          <p className="text-sm flashana-subtitle mt-1">
            Manage your personal information and account settings
          </p>
        </div>

        {/* Success Message */}
        {successMessage && (
          <div className="mb-4 p-4 bg-green-50 border border-green-200 rounded-lg flex items-center">
            <AlertCircle className="h-5 w-5 text-green-600 mr-2" />
            <span className="text-green-800">{successMessage}</span>
          </div>
        )}

        {/* Profile Card */}
        <div className="flashana-card">
          <div className="p-6">
            <div className="flex justify-between items-start mb-6">
              <h2 className="text-lg font-semibold flashana-body-text">
                Personal Information
              </h2>
              {!isEditing && (
                <button
                  onClick={() => setIsEditing(true)}
                  className="flashana-button-secondary flex items-center space-x-2"
                >
                  <Edit2 className="h-4 w-4" />
                  <span>Edit Profile</span>
                </button>
              )}
            </div>

            <form onSubmit={handleSubmit}>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                {/* Avatar Section */}
                <div className="md:col-span-1">
                  <div className="flex flex-col items-center">
                    <div className="relative group">
                      {formData.avatar ? (
                        <img
                          src={formData.avatar}
                          alt={formData.name}
                          className="w-32 h-32 rounded-full object-cover"
                        />
                      ) : (
                        <div className="w-32 h-32 bg-primary rounded-full flex items-center justify-center">
                          <span className="text-3xl font-semibold text-white">
                            {getInitials(formData.name || "U")}
                          </span>
                        </div>
                      )}
                      {isEditing && (
                        <button
                          type="button"
                          className="absolute bottom-0 right-0 p-2 bg-gray-900 rounded-full text-white hover:bg-gray-800 transition-colors"
                        >
                          <Camera className="h-4 w-4" />
                        </button>
                      )}
                    </div>
                    <p className="mt-2 text-sm text-gray-500">
                      {isEditing ? "Click to upload photo" : user.role}
                    </p>
                  </div>
                </div>

                {/* Form Fields */}
                <div className="md:col-span-2 space-y-4">
                  {/* Name Field */}
                  <div>
                    <label className="block text-sm font-medium flashana-body-text mb-1">
                      Full Name
                    </label>
                    {isEditing ? (
                      <>
                        <input
                          type="text"
                          value={formData.name}
                          onChange={(e) =>
                            setFormData({ ...formData, name: e.target.value })
                          }
                          className="flashana-input"
                          placeholder="Enter your full name"
                        />
                        {errors.name && (
                          <p className="mt-1 text-sm text-red-600">
                            {errors.name}
                          </p>
                        )}
                      </>
                    ) : (
                      <p className="py-2 text-gray-900">{user.username}</p>
                    )}
                  </div>

                  {/* Email Field */}
                  <div>
                    <label className="block text-sm font-medium flashana-body-text mb-1">
                      Email Address
                    </label>
                    {isEditing ? (
                      <>
                        <div className="relative">
                          <input
                            type="email"
                            value={formData.email}
                            onChange={(e) =>
                              setFormData({
                                ...formData,
                                email: e.target.value,
                              })
                            }
                            className="flashana-input pl-10"
                            placeholder="Enter your email"
                          />
                          <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
                        </div>
                        {errors.email && (
                          <p className="mt-1 text-sm text-red-600">
                            {errors.email}
                          </p>
                        )}
                      </>
                    ) : (
                      <div className="flex items-center space-x-2 py-2">
                        <Mail className="h-5 w-5 text-gray-400" />
                        <p className="text-gray-900">{user.email}</p>
                      </div>
                    )}
                  </div>

                  {/* Role Field */}
                  <div>
                    <label className="block text-sm font-medium flashana-body-text mb-1">
                      Role
                    </label>
                    {isEditing ? (
                      <select
                        value={formData.role}
                        onChange={(e) =>
                          setFormData({ ...formData, role: e.target.value })
                        }
                        className="flashana-input"
                      >
                        <option value="Admin">Admin</option>
                        <option value="Manager">Manager</option>
                        <option value="Analyst">Analyst</option>
                        <option value="User">User</option>
                      </select>
                    ) : (
                      <div className="flex items-center space-x-2 py-2">
                        <Shield className="h-5 w-5 text-gray-400" />
                        <p className="text-gray-900">{user.role}</p>
                      </div>
                    )}
                  </div>

                  {/* Action Buttons */}
                  {isEditing && (
                    <div className="flex space-x-3 pt-4">
                      <button
                        type="submit"
                        className="flashana-button flex items-center space-x-2"
                      >
                        <Save className="h-4 w-4" />
                        <span>Save Changes</span>
                      </button>
                      <button
                        type="button"
                        onClick={handleCancel}
                        className="flashana-button-secondary flex items-center space-x-2"
                      >
                        <X className="h-4 w-4" />
                        <span>Cancel</span>
                      </button>
                    </div>
                  )}
                </div>
              </div>
            </form>
          </div>
        </div>

        {/* Additional Information Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-6">
          {/* Account Information */}
          <div className="flashana-card">
            <div className="p-6">
              <h3 className="text-lg font-semibold flashana-body-text mb-4">
                Account Information
              </h3>
              <div className="space-y-3">
                <div>
                  <p className="text-sm text-gray-500">User ID</p>
                  <p className="font-mono text-sm mt-1">{user.user_id}</p>
                </div>
                <div>
                  <p className="text-sm text-gray-500">Account Created</p>
                  <p className="text-sm mt-1">January 15, 2024</p>
                </div>
                <div>
                  <p className="text-sm text-gray-500">Last Login</p>
                  <p className="text-sm mt-1">
                    Today at {new Date().toLocaleTimeString()}
                  </p>
                </div>
              </div>
            </div>
          </div>

          {/* Security Settings */}
          <div className="flashana-card">
            <div className="p-6">
              <h3 className="text-lg font-semibold flashana-body-text mb-4">
                Security Settings
              </h3>
              <div className="space-y-3">
                <button className="w-full text-left p-3 rounded-lg border border-gray-200 hover:border-gray-300 transition-colors">
                  <div className="flex justify-between items-center">
                    <span className="text-sm font-medium">Change Password</span>
                    <span className="text-xs text-gray-500">→</span>
                  </div>
                </button>
                <button className="w-full text-left p-3 rounded-lg border border-gray-200 hover:border-gray-300 transition-colors">
                  <div className="flex justify-between items-center">
                    <span className="text-sm font-medium">
                      Two-Factor Authentication
                    </span>
                    <span className="text-xs text-green-600">Enabled</span>
                  </div>
                </button>
                <button className="w-full text-left p-3 rounded-lg border border-gray-200 hover:border-gray-300 transition-colors">
                  <div className="flex justify-between items-center">
                    <span className="text-sm font-medium">Login History</span>
                    <span className="text-xs text-gray-500">→</span>
                  </div>
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
