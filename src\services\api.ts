import axios, { AxiosInstance, AxiosProgressEvent } from 'axios';
import {
  Session,
  FileUploadResponse,
  ProcessFilesRequest,
  ProcessFilesResponse,
  ValidationRequest,
  ValidationResponse,
} from '../types';
import { transformValidationResponse } from '../utils/transformers';

class ComplianceAPI {
  private client: AxiosInstance;

  constructor() {
    this.client = axios.create({
      baseURL: import.meta.env.VITE_API_URL || 'http://localhost:15000/api',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    // Request interceptor
    this.client.interceptors.request.use(
      (config) => {
        // Add any auth tokens here if needed
        return config;
      },
      (error) => {
        return Promise.reject(error);
      }
    );

    // Response interceptor
    this.client.interceptors.response.use(
      (response) => {
        return response;
      },
      (error) => {
        if (error.response) {
          // Handle specific error codes
          if (error.response.status === 401) {
            // Handle unauthorized
          } else if (error.response.status === 500) {
            // Handle server error
          }
        }
        return Promise.reject(error);
      }
    );
  }

  // Health check
  healthCheck = async () => {
    const response = await this.client.get('/health');
    return response.data;
  }

  // Create validation session
  createSession = async (): Promise<Session> => {
    const response = await this.client.post<Session>('/sessions');
    return response.data;
  }

  // Get session details
  getSessionDetails = async (sessionId: string): Promise<Session> => {
    const response = await this.client.get<Session>(`/sessions/${sessionId}/results`);
    return response.data;
  }

  // Upload all files together
  uploadFiles = async (
    sessionId: string,
    formData: FormData,
    onUploadProgress?: (progressEvent: AxiosProgressEvent) => void
  ): Promise<FileUploadResponse> => {
    const response = await this.client.post<FileUploadResponse>(`/sessions/${sessionId}/upload`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
      onUploadProgress,
    });
    return response.data;
  }

  // Process files
  processFiles = async (data: ProcessFilesRequest): Promise<ProcessFilesResponse> => {
    const response = await this.client.post<ProcessFilesResponse>(`/sessions/${data.session_id}/process`, {});
    return response.data;
  }

  // Run validation
  validateFiles = async (data: ValidationRequest): Promise<ValidationResponse> => {
    const response = await this.client.post<any>(`/sessions/${data.session_id}/validate`, {});
    // The validate endpoint might return the same format as results
    if (response.data.validation_results && Array.isArray(response.data.validation_results)) {
      return transformValidationResponse(response.data);
    }
    return response.data;
  }

  // Get validation results
  getValidationResults = async (sessionId: string): Promise<ValidationResponse> => {
    const response = await this.client.get<any>(`/sessions/${sessionId}/results`);
    return transformValidationResponse(response.data);
  }
}

// Export singleton instance
export const api = new ComplianceAPI();