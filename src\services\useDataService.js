import axios from "axios";
import { useQuery, QueryClient } from "@tanstack/react-query";
import { API_URL } from "../constants/urls";
import { useAuthStore } from "@/store/authStore";

const getToken = () => {
  return useAuthStore.getState().user?.access_token || '';
};
const token = getToken();

const logout = useAuthStore.getState().logout;

const updateAxiosInstance = (accessToken) => {
    apiClient.defaults.headers.Authorization = accessToken ? accessToken : '';
};

const apiClient = axios.create({
    baseURL: API_URL,
    headers: {
        "Content-type": "application/json",
    },
});

apiClient.interceptors.request.use((config) => {
  const token = getToken();
  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }
  return config;
});

// const apiDeleteClient = axios.create({
//     baseURL: "",
//     headers: {
//         "Content-type": "application/json",
//         Authorization: `${token}`,
//     },
// });

// const updateAxiosInstanceForFileData = (accessToken) => {
//     apiClient.defaults.headers = {
//         "Content-type": "multipart/form-data; boundary=<calculated when request is sent>",
//         Authorization: `${token}`,
//     };
// };

const commentsService = async (endpoint) => {
    try {
        const response = await apiClient.get(endpoint);
        if (response.status === 200) {
            return response.data;
        }
        throw new Error(response.statusText);
    } catch (err) {
        return Promise.reject(err.message);
    }
};

const getService = async (endpoint, data) => {
    try {
        const response = await apiClient.get(endpoint, { data });
        if ([200, 201, 204].includes(response.status)) {
            return response.data;
        }
        throw new Error(response.statusText);
    } catch (err) {
        if (
            err.code === "ERR_NETWORK" ||
            err.response?.data?.message === "Unauthorized"
        ) {
            localStorage.clear()
            logout()
            localStorage.clear()
            logout()
        }
        return Promise.reject(err.response?.data);
    }
};

const postService = async (endpoint, data) => {
    try {
        const response = await apiClient.post(endpoint, data);
        if ([200, 201, 204].includes(response.status)) {
            return response.data;
        }
        throw new Error(response.statusText);
    } catch (err) {
         if (
            err.code === "ERR_NETWORK" ||
            err.response?.data?.message === "Unauthorized"
        ) {
             localStorage.clear()
             logout()
        }  
         if (
            err.code === "ERR_NETWORK" ||
            err.response?.data?.message === "Unauthorized"
        ) {
             localStorage.clear()
             logout()
        }  
        return Promise.reject(err.response?.data);
    }
};

const putService = async (endpoint, data) => {
    try {
        const response = await apiClient.put(endpoint, data);
        if ([200, 201, 204].includes(response.status)) {
            return response.data;
        }
        throw new Error(response.statusText);
    } catch (err) {
         if (
            err.code === "ERR_NETWORK" ||
            err.response?.data?.message === "Unauthorized"
        ) {
             localStorage.clear()
            logout()
        }
         if (
            err.code === "ERR_NETWORK" ||
            err.response?.data?.message === "Unauthorized"
        ) {
             localStorage.clear()
            logout()
        }
        return Promise.reject(err.response?.data);
    }
};

const putServiceForFileData = async (endpoint, data) => {
    try {
        updateAxiosInstanceForFileData();
        const response = await apiClient.put(endpoint, data);
        if ([200, 201, 204].includes(response.status)) {
            return response.data;
        }
        throw new Error(response.statusText);
    } catch (err) {
         if (
            err.code === "ERR_NETWORK" ||
            err.response?.data?.message === "Unauthorized"
        ) {
            localStorage.clear()
            logout()
        }
         if (
            err.code === "ERR_NETWORK" ||
            err.response?.data?.message === "Unauthorized"
        ) {
            localStorage.clear()
            logout()
        }
        return Promise.reject(err.response?.data);
    }
};

const deleteService = async (endpoint, data) => {
    try {
        // Use apiClient instead of apiDeleteClient
        const response = await apiClient.delete(endpoint, { data });
        if ([200, 201, 204].includes(response.status)) {
            return response.data;
        }
        throw new Error(response.statusText);
    } catch (err) {
         if (
            err.code === "ERR_NETWORK" ||
            err.response?.data?.message === "Unauthorized"
        ) {
            localStorage.clear()
            logout()
        }
         if (
            err.code === "ERR_NETWORK" ||
            err.response?.data?.message === "Unauthorized"
        ) {
            localStorage.clear()
            logout()
        }
        return Promise.reject(err.response?.data);
    }
};

const authenticationPostService = async (
    endpoint,
    data,
    twoFAVerificationToken
) => {
    const options = {
        headers: {
            "Content-type": "application/json",
            ...(twoFAVerificationToken && {
                Authorization: `Bearer ${twoFAVerificationToken}`,
            }),
        },
    };
    try {
        const response = await axios.post(`${API_URL}${endpoint}`, data, options);
        return response;
    } catch (err) {
         if (
            err.code === "ERR_NETWORK" ||
            err.response?.data?.message === "Unauthorized"
        ) {
            localStorage.clear()
            logout()        }
        return Promise.reject(err.response?.data);
    }
};

const getServiceWithoutToken = async (endpoint, data) => {
    try {
        const response = await axios.get(endpoint, { data });
        if ([200, 201, 204].includes(response.status)) {
            return response.data;
        }
        throw new Error(response.statusText);
    } catch (err) {
        
        
        return Promise.reject(err.response?.data);
    }
};

const patchService = async (endpoint, data) => {
    try {
        const response = await apiClient.patch(endpoint, data);
        if ([200, 201, 204].includes(response.status)) {
            return response.data;
        }
        throw new Error(response.statusText);
    } catch (err) {
         if (
            err.code === "ERR_NETWORK" ||
            err.response?.data?.message === "Unauthorized"
        ) {
            localStorage.clear()
            logout()        }
        return Promise.reject(err.response?.data);
    }
};

const putServiceWithoutToken = async (endpoint, data) => {
    const options = {
        headers: {
            "Content-type": "application/json",
        },
    };
    try {
        const response = await axios.put(`${API_URL}${endpoint}`, data, options);
        return response;
    } catch (err) {
        return Promise.reject(err.response?.data);
    }
};

const useSharedState = (key, initialValue) => {
    const queryClient = new QueryClient();
    const { data: state } = useQuery(key, () => queryClient.getQueryData(key), {
        initialData: initialValue,
    });
    const setState = (value) => queryClient.setQueryData(key, value);
    return [state, setState];
};

const useDataService = {
    commentsService,
    getService,
    postService,
    putService,
    deleteService,
    patchService,
    authenticationPostService,
    getServiceWithoutToken,
    putServiceWithoutToken,
    useSharedState,
    putServiceForFileData,
    updateAxiosInstance,
};

export default useDataService;