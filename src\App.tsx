import { RouterProvider } from "react-router-dom";
import { Toaster } from "react-hot-toast";
import { ThemeProvider } from "./contexts/ThemeContext";
import { router } from "./router";
import "./index.css";
import { ToastContainer } from "@/components/common/Toast";
import { useToast } from "@/hooks/useToast";

function App() {
  const { toasts, removeToast } = useToast();

  return (
    <ThemeProvider>
      <RouterProvider router={router} />
      <Toaster
        position="bottom-right"
        toastOptions={{
          duration: 5000,
          style: {
            background: "rgba(255, 255, 255, 0.1)",
            backdropFilter: "blur(10px)",
            border: "1px solid rgba(255, 255, 255, 0.2)",
            color: "white",
          },
          success: {
            iconTheme: {
              primary: "#4CAF50",
              secondary: "white",
            },
          },
          error: {
            iconTheme: {
              primary: "#f87171",
              secondary: "white",
            },
          },
        }}
      />
      <ToastContainer toasts={toasts} onClose={removeToast} />
    </ThemeProvider>
  );
}

export default App;
