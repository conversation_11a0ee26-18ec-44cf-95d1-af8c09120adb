export interface DemoUser {
  id: string;
  email: string;
  password: string;
  name: string;
  role: 'Admin' | 'Manager' | 'Analyst' | 'User';
  avatar: string;
  permissions: string[];
  description: string;
}

export const demoUsers: DemoUser[] = [
  {
    id: '1',
    email: '<EMAIL>',
    password: 'admin123',
    name: '<PERSON>',
    role: 'Admin',
    avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=32&h=32&fit=crop&crop=face',
    permissions: ['all'],
    description: 'Full system access - can manage all modules, users, and configurations'
  },
  {
    id: '2',
    email: '<EMAIL>',
    password: 'manager123',
    name: '<PERSON>',
    role: 'Manager',
    avatar: 'https://images.unsplash.com/photo-1494790108755-2616b69d4010?w=32&h=32&fit=crop&crop=face',
    permissions: ['dashboard', 'stores', 'company', 'data-ingestion', 'forecasting', 'reports'],
    description: 'Manager access - can view all data, manage stores, and access forecasting'
  },
  {
    id: '3',
    email: '<EMAIL>',
    password: 'analyst123',
    name: '<PERSON>',
    role: 'Analyst',
    avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=32&h=32&fit=crop&crop=face',
    permissions: ['dashboard', 'data-ingestion', 'forecasting', 'reports'],
    description: 'Data analyst access - can work with data ingestion, mapping, validation, and forecasting'
  },
  {
    id: '4',
    email: '<EMAIL>',
    password: 'user123',
    name: 'Emma Wilson',
    role: 'User',
    avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=32&h=32&fit=crop&crop=face',
    permissions: ['dashboard', 'stores'],
    description: 'Basic user access - can view dashboard and manage assigned stores only'
  }
];

export const getDemoUserByCredentials = (email: string, password: string): DemoUser | null => {
  return demoUsers.find(user => user.email === email && user.password === password) || null;
};

export const getAllDemoUsers = (): DemoUser[] => {
  return demoUsers;
};