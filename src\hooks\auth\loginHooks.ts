/* eslint-disable @typescript-eslint/no-explicit-any */

import { useMutation, useQueryClient } from "@tanstack/react-query";
import useDataService from "../../services/useDataService";
import { SIGNIN_URL } from "../../constants/urls";

export const usePostSignin = (onPostSuccess: any, onPostError: any) => {
  const queryClient = useQueryClient();
  const {
    mutate: postMutate,
  } = useMutation<any, Error, { data: any; options?: any }>({
    mutationFn: async ({ data, options }: { data: any; options?: any }) => {
      const result = await useDataService.postService(`${SIGNIN_URL}`, data);
      return { ...result, rememberMe: options?.rememberMe };
    },
    onSuccess: (data: any) => {
      onPostSuccess(data, data.rememberMe);
    },
    onSettled: () => {
      queryClient.invalidateQueries({ queryKey: ["signin"] });
    },
    onError: (error: any) => {
      onPostError(error);
    },
  });

  return {
    postMutate: (data: any, options?: any) => postMutate({ data, options })
  };
};
