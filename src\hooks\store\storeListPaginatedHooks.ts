import { useQuery, useMutation } from '@tanstack/react-query';
import useDataService from '../../services/useDataService';
import { GET_STORELIST_URL } from '../../constants/urls';

interface StoreListParams {
  location_name?: string;
  page?: number;
  size?: number;
  sort_column?: string;
  sort_order?: 'asc' | 'desc' | 1 | -1;
}

export interface Store {
  id: string;
  location_name: string;
  [key: string]: any;
}

export interface StoreListResponse {
  data: Store[];
  total: number;
  page: number;
  size: number;
  [key: string]: any;
}

export const useGetStoreList = (params: StoreListParams, enabled = true) => {
  return useQuery<StoreListResponse, Error>({
    queryKey: ['store-list', params],
    queryFn: async () => {
      const searchParams = new URLSearchParams();
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          searchParams.append(key, String(value));
        }
      });
      const endpoint = `${GET_STORELIST_URL}?${searchParams.toString()}`;
      return await useDataService.getService(endpoint);
    },
    staleTime:0,
    enabled
  }
);
};