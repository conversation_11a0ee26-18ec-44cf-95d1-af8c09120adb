import { useQuery } from "@tanstack/react-query";
import useDataService from "@/services/useDataService";
import { STORE_URL } from "@/constants/urls";

export interface Store {
  location_id: number;
  location_name: string;
}

interface GetStoreListResponse {
  success: boolean;
  message: string;
  code: number;
  data: Store[];
}

export const useGetStoreList = (onSuccessData?: any, apiCall?: any, onErrorData?: (error: any) => void, currentPage?: any, sortOrder?: any) => {
  return useQuery<GetStoreListResponse>({
    queryKey: ["store-list"],
    queryFn: async () => {
      return await useDataService.getService(STORE_URL);
    },
    staleTime: 0,              // ✅ Cache forever
  });
};