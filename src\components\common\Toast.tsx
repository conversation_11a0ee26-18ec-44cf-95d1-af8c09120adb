import { useState, useEffect } from "react";
import { X, CheckCircle, AlertCircle, Info, AlertTriangle } from "lucide-react";

interface ToastProps {
  id: string;
  type: "success" | "error" | "warning" | "info";
  title: string;
  message?: string;
  duration?: number;
  onClose: (id: string) => void;
}

export function Toast({
  id,
  type,
  title,
  message,
  duration = 4000,
  onClose,
}: ToastProps) {
  useEffect(() => {
    const timer = setTimeout(() => {
      onClose(id);
    }, duration);

    return () => clearTimeout(timer);
  }, [id, duration, onClose]);

  const getIcon = () => {
    switch (type) {
      case "success":
        return <CheckCircle size={20} style={{ color: "#16a34a" }} />;
      case "error":
        return <AlertCircle size={20} style={{ color: "#dc2626" }} />;
      case "warning":
        return <AlertTriangle size={20} style={{ color: "#d97706" }} />;
      case "info":
        return <Info size={20} style={{ color: "#2563eb" }} />;
    }
  };

  const getStyles = () => {
    switch (type) {
      case "success":
        return {
          backgroundColor: "#f0fdf4",
          borderColor: "#bbf7d0",
          titleColor: "#166534",
          messageColor: "#16a34a",
        };
      case "error":
        return {
          backgroundColor: "#fef2f2",
          borderColor: "#fecaca",
          titleColor: "#991b1b",
          messageColor: "#dc2626",
        };
      case "warning":
        return {
          backgroundColor: "#fef3c7",
          borderColor: "#fed7aa",
          titleColor: "#92400e",
          messageColor: "#d97706",
        };
      case "info":
        return {
          backgroundColor: "#eff6ff",
          borderColor: "#bfdbfe",
          titleColor: "#1e40af",
          messageColor: "#2563eb",
        };
    }
  };

  const styles = getStyles();

  return (
    <div
      style={{
        backgroundColor: styles.backgroundColor,
        border: `1px solid ${styles.borderColor}`,
        borderRadius: "0.5rem",
        padding: "1rem",
        boxShadow:
          "0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)",
        maxWidth: "400px",
        animation: "slideIn 0.3s ease-out",
      }}
    >
      <div
        style={{ display: "flex", alignItems: "flex-start", gap: "0.75rem" }}
      >
        {getIcon()}
        <div style={{ flex: 1 }}>
          <div
            style={{
              fontSize: "0.875rem",
              fontWeight: "600",
              color: styles.titleColor,
              marginBottom: message ? "0.25rem" : 0,
            }}
          >
            {title}
          </div>
          {message && (
            <div style={{ fontSize: "0.75rem", color: styles.messageColor }}>
              {message}
            </div>
          )}
        </div>
        <button
          onClick={() => onClose(id)}
          style={{
            padding: "0.25rem",
            color: "#71717a",
            backgroundColor: "transparent",
            border: "none",
            borderRadius: "0.25rem",
            cursor: "pointer",
            transition: "color 0.2s",
          }}
          onMouseOver={(e) => (e.currentTarget.style.color = "#3f3f46")}
          onMouseOut={(e) => (e.currentTarget.style.color = "#71717a")}
        >
          <X size={16} />
        </button>
      </div>

      <style>{`
        @keyframes slideIn {
          from {
            transform: translateX(100%);
            opacity: 0;
          }
          to {
            transform: translateX(0);
            opacity: 1;
          }
        }
      `}</style>
    </div>
  );
}

interface ToastContainerProps {
  toasts: Array<{
    id: string;
    type: "success" | "error" | "warning" | "info";
    title: string;
    message?: string;
    duration?: number;
  }>;
  onClose: (id: string) => void;
}

export function ToastContainer({ toasts, onClose }: ToastContainerProps) {
  return (
    <div
      style={{
        position: "fixed",
        top: "1rem",
        right: "1rem",
        zIndex: 9999,
        display: "flex",
        flexDirection: "column",
        gap: "0.75rem",
      }}
    >
      {toasts.map((toast) => (
        <Toast
          key={toast.id}
          id={toast.id}
          type={toast.type}
          title={toast.title}
          message={toast.message}
          duration={toast.duration}
          onClose={onClose}
        />
      ))}
    </div>
  );
}
