import { useMutation, useQuery } from '@tanstack/react-query';
import useDataService from '../../services/useDataService';
import { GET_TENANT_URL, UPDATE_TENANT_URL, GET_COMPANY_TYPES_URL } from '../../constants/urls';

interface CompanyDetails {
    tenant_name: string;
    website_url?: string;
    number_of_locations: number;
    tax_id?: string;
    street_address?: string;
    city?: string;
    state?: string;
    postal_code?: string;
    country?: string;
    [key: string]: any;
}

export const useGetCompanyDetails = (tenantId: string, enabled = true) => {
    return useQuery<CompanyDetails, Error>({
        queryKey: ['company-details', tenantId],
        queryFn: async () => {
            const endpoint = `${GET_TENANT_URL}/${tenantId}`;
            return await useDataService.getService(endpoint);
        },
        enabled: !!tenantId && enabled,
        staleTime: 0,            
    });
};

export const useUpdateCompanyDetails = () => {
    return useMutation<CompanyDetails, Error, { tenantId: string; data: CompanyDetails }>({
        mutationFn: async ({ tenantId, data }) => {
            const endpoint = `${UPDATE_TENANT_URL}/${tenantId}`;
            return await useDataService.patchService(endpoint, data);
        },
    });
};

export interface CompanyType {
    industry_id: number;
    industry_name: string;
    description: string;
    is_active: boolean;
    created_at: string;
}
 
export const useGetCompanyTypes = (enabled = true) => {
    return useQuery<CompanyType[], Error>({
        queryKey: ['company-types'],
        queryFn: async () => {
            const response = await useDataService.getService(GET_COMPANY_TYPES_URL);
            return response?.result?.data || [];
        },
        enabled,
        staleTime: 0,
    });
};