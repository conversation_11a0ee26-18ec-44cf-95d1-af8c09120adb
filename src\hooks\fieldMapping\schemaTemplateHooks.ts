import { useQuery } from "@tanstack/react-query";
import { GET_SCHEMA_TEMPLATE_URL } from "@/constants/urls";

// Types for the schema template API response
export interface SchemaTemplateColumn {
  schema_template_column_id: number;
  schema_template_column_name: string;
  is_mapped: boolean;
  data_type: string;
  is_required: boolean;
}

export interface SchemaTemplate {
  schema_template_id: number;
  schema_template_name: string;
  columns: SchemaTemplateColumn[];
}

export interface SchemaTemplateResponse {
  data: SchemaTemplate[];
}

// API function to fetch schema template
const fetchSchemaTemplate = async (batchId: number): Promise<SchemaTemplateResponse> => {
  const token = localStorage.getItem("token");
  
  if (!token) {
    throw new Error("No authentication token found");
  }

  const response = await fetch(`${GET_SCHEMA_TEMPLATE_URL}/${batchId}`, {
    method: "GET",
    headers: {
      "Content-Type": "application/json",
      Authorization: `Bearer ${token}`,
    },
  });

  if (!response.ok) {
    throw new Error(`Failed to fetch schema template: ${response.statusText}`);
  }

  return response.json();
};

// Hook to get schema template
export const useGetSchemaTemplate = (batchId: number) => {
  return useQuery({
    queryKey: ["schemaTemplate", batchId],
    queryFn: () => fetchSchemaTemplate(batchId),
    enabled: !!batchId && batchId > 0,
    staleTime: 5 * 60 * 1000, // 5 minutes
    retry: 2,
  });
};
