import { useQuery } from "@tanstack/react-query";
import useDataService from "@/services/useDataService";
import { GET_SCHEMA_TEMPLATE_URL } from "@/constants/urls";

// Types for the schema template API response
export interface SchemaTemplateColumn {
  schema_template_column_id: number;
  schema_template_column_name: string;
  is_mapped: boolean;
  data_type: string;
  is_required: boolean;
}

export interface SchemaTemplate {
  schema_template_id: number;
  schema_template_name: string;
  columns: SchemaTemplateColumn[];
}

export interface SchemaTemplateResponse {
  data: SchemaTemplate[];
}

// Hook to get schema template
export const useGetSchemaTemplate = (batchId: number, enabled = true) => {
  return useQuery<SchemaTemplateResponse, Error>({
    queryKey: ["schemaTemplate", batchId],
    queryFn: async () => {
      const endpoint = `${GET_SCHEMA_TEMPLATE_URL}/${batchId}`;
      return await useDataService.getService(endpoint);
    },
    enabled: enabled && !!batchId && batchId > 0,
    staleTime: 5 * 60 * 1000, // 5 minutes
    retry: 2,
  });
};
