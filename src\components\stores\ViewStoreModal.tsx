import {
  <PERSON><PERSON>,
  <PERSON>alog<PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "../ui/button";

interface ViewStoreModalProps {
  isOpen: boolean;
  onClose: () => void;
}

export default function ViewStoreModal({ isOpen, onClose }: ViewStoreModalProps) {
  if (!isOpen) return null;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-3xl">
        <DialogHeader>
          <DialogTitle className="text-lg font-semibold">View Store</DialogTitle>
        </DialogHeader>

        <div>
          {/* Store Details */}
          <section className="p-4 border-b">
            <h3 className="text-md font-semibold text-secondary mb-3">Store Details</h3>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
              <Detail label="Store Code / Name" value="HTR123" />
              <Detail label="Store Type" value="Grocery" />
              <Detail label="Approximate SKU Count" value="1-100" />
              <Detail label="Status" value="Active" />
            </div>
          </section>

          {/* Location Details */}
          <section className="p-4 border-b">
            <h3 className="text-md font-semibold text-secondary mb-3">Location Details</h3>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
              <Detail label="Country" value="Canada" />
              <Detail label="State / Region" value="Ontario" />
              <Detail label="City" value="Toronto" />
              <Detail label="ZIP/Postal Code" value="1232334" />
            </div>
            <div className="mt-4 text-sm">
              <div className="text-gray-500">Street Address</div>
              <div className="font-semibold">
                CN Tower 290 Bremner Blvd, Toronto, ON M5V 3L9, Canada
              </div>
            </div>
          </section>

          {/* Store Contact Info */}
          <section className="p-4 border-b">
            <h3 className="text-md font-semibold text-secondary mb-3">Store Contact Info</h3>
            <div className="grid grid-cols-2 gap-4 text-sm">
              <Detail label="Primary Contact Name" value="John Doe" />
              <Detail label="Contact Email" value="<EMAIL>" />
            </div>
          </section>
            {/* <div className="flex gap-4 justify-end p-6">
              <Button
                type="button"
                variant="default"
                className="text-white"
              >
                Cancel
              </Button>
        </div> */}
        </div>
      </DialogContent>
    </Dialog>
  );
}

function Detail({ label, value }: { label: string; value: string }) {
  return (
    <div>
      <div className="text-gray-500">{label}</div>
      <div className="font-semibold">{value}</div>
    </div>
  );
}
