import { useMutation, useQuery } from '@tanstack/react-query';
import { useSnackbar } from 'notistack';
import { api } from '../services/api';
import { ProcessFilesRequest, ValidationRequest, ValidationResponse } from '../types';

export const useValidation = () => {
  const { enqueueSnackbar } = useSnackbar();

  const processFilesMutation = useMutation({
    mutationFn: (data: ProcessFilesRequest) => api.processFiles(data),
    onSuccess: () => {
      enqueueSnackbar('Files processed successfully', { variant: 'success' });
    },
    onError: (error: Error) => {
      enqueueSnackbar(`Failed to process files: ${error.message}`, {
        variant: 'error',
      });
    },
  });

  const validateFilesMutation = useMutation<ValidationResponse, Error, ValidationRequest>({
    mutationFn: api.validateFiles,
    onSuccess: (data) => {
      const hasErrors = data.validation_results.some(
        (result) => result.issues.filter((issue) => issue.severity === 'error').length > 0
      );
      
      if (hasErrors) {
        enqueueSnackbar('Validation completed with errors', { variant: 'warning' });
      } else {
        enqueueSnackbar('Validation completed successfully', { variant: 'success' });
      }
    },
    onError: (error) => {
      enqueueSnackbar(`Validation failed: ${error.message}`, {
        variant: 'error',
      });
    },
  });

  const useValidationResults = (sessionId?: string) => {
    return useQuery<ValidationResponse>({
      queryKey: ['validation', sessionId],
      queryFn: () => api.getValidationResults(sessionId!),
      enabled: !!sessionId,
    });
  };

  return {
    processFiles: processFilesMutation.mutate,
    processFilesAsync: processFilesMutation.mutateAsync,
    isProcessingFiles: processFilesMutation.isPending,
    validateFiles: validateFilesMutation.mutate,
    validateFilesAsync: validateFilesMutation.mutateAsync,
    isValidating: validateFilesMutation.isPending,
    useValidationResults,
  };
};