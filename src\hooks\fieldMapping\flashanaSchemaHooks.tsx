import { useQuery } from "@tanstack/react-query";
import useDataService from "@/services/useDataService";
import { GET_FLASHANA_SCHEMA_URL } from "@/constants/urls";

// export interface MappedField {
//   schema_template_column_id: number;
//   schema_template_column_name: string;
// }

// export interface SchemaColumn {
//   flashana_schema_column_id: number;
//   flashana_schema_column_name: string;
//   mapped_fields: MappedField[];
//   is_mapped: boolean;
//   is_multi_field: boolean;
//   data_type: string;
//   is_required: boolean;
// }

// export interface FlashanaSchema {
//   flashana_schema_id: number;
//   flashana_schema_name: string;
//   columns: SchemaColumn[];
// }

// export interface SchemaBatchInfo {
//   uploaded_file_batch_id: number;
//   batch_name: string;
//   uploaded_at: string;
// }

// export interface FlashanaSchemaData {
//   batch_info: SchemaBatchInfo;
//   flashana_schemas: FlashanaSchema[];
// }

// export interface FlashanaSchemaApiResponse {
//   success: boolean;
//   message: string;
//   code: number;
//   result: {
//     data: FlashanaSchemaData;
//   };
// }

// export const useGetFlashanaSchema = (batchId: number, enabled = true) => {
//   return useQuery<FlashanaSchemaApiResponse, Error>({
//     queryKey: ["flashana-schema", batchId],
//     queryFn: async () => {
//       const endpoint = `${GET_FLASHANA_SCHEMA_URL}/${batchId}`;
//       return await useDataService.getService(endpoint);
//     },
//     enabled,
//   });
// };

export interface MappedField {
  schema_template_column_id: number;
  schema_template_column_name: string;
}

export interface SchemaColumn {
  flashana_schema_column_id: number;
  flashana_schema_column_name: string;
  mapped_fields: MappedField[];
  is_mapped: boolean;
  is_multi_field: boolean;
  data_type: string;
  is_required: boolean;
}

export interface FlashanaSchema {
  flashana_schema_id: number;
  flashana_schema_name: string;
  columns: SchemaColumn[];
}

export interface FlashanaSchemaApiResponse {
  success: boolean;
  message: string;
  code: number;
  result: {
    data: FlashanaSchema[];
  };
}

export const useGetFlashanaSchema = (batchId: number, enabled = true) => {
  return useQuery<FlashanaSchemaApiResponse, Error>({
    queryKey: ["flashana-schema", batchId],
    queryFn: async () => {
      const endpoint = `${GET_FLASHANA_SCHEMA_URL}/${batchId}`;
      return await useDataService.getService(endpoint);
    },
    enabled,
  });
};
