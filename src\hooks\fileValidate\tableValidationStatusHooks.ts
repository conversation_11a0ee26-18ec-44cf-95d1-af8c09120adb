import { useQuery } from '@tanstack/react-query';
import useDataService from '@/services/useDataService';
import { GET_TABLE_VALIDATION_STATUS_URL } from '@/constants/urls';

export interface TableValidationStatus {
  is_product_categories_valid: boolean;
  is_product_valid: boolean;
  is_skus_valid: boolean;
  is_customers_valid: boolean;
  is_locations_valid: boolean;
  is_sales_data_valid: boolean;
  consolidated_status: string;
}

export interface TableValidationApiResponse {
  success: boolean;
  message: string;
  code: number;
  result: {
    data: TableValidationStatus;
  };
}

export const useGetTableValidationStatus = (
  batchId: string | number,
  enabled = true
) => {
  const endpoint = `${GET_TABLE_VALIDATION_STATUS_URL}/${batchId}`;

  return useQuery<TableValidationApiResponse, Error>({
    queryKey: ['table-validation-status', batchId],
    queryFn: async () => await useDataService.getService(endpoint),
    enabled: !!batchId && enabled,
  });
};
