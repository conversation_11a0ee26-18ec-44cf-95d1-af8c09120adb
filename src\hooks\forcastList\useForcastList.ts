

import { useQuery } from "@tanstack/react-query";
import useDataService from "@/services/useDataService";
import { GETFORECAST_NAME_LIST_URL } from "@/constants/urls";
 

export const fetchForecastDropdown = () => {
  return useQuery({
    queryKey: ["forecastDropdown"],
    queryFn: async () => {
      const endpoint = `${GETFORECAST_NAME_LIST_URL}`;
      return await useDataService.getService(endpoint);
    },
    staleTime: 0,            
  });
};