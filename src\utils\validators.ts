export const validateFileType = (file: File, allowedTypes: string[]): boolean => {
  const fileExtension = file.name.split('.').pop()?.toLowerCase();
  return allowedTypes.includes(fileExtension || '');
};

export const validateFileSize = (file: File, maxSizeInMB: number): boolean => {
  const maxSizeInBytes = maxSizeInMB * 1024 * 1024;
  return file.size <= maxSizeInBytes;
};

export const getFileTypeFromName = (fileName: string): string => {
  const lowerName = fileName.toLowerCase();
  
  if (lowerName.includes('payroll')) return 'payroll';
  if (lowerName.includes('attendance')) return 'attendance';
  if (lowerName.includes('government') || lowerName.includes('report')) return 'government';
  
  return 'unknown';
};

export const validateRequiredFiles = (files: File[]): {
  isValid: boolean;
  missingTypes: string[];
} => {
  const requiredTypes = ['payroll', 'attendance', 'government'];
  const uploadedTypes = files.map(file => getFileTypeFromName(file.name));
  
  const missingTypes = requiredTypes.filter(
    type => !uploadedTypes.includes(type)
  );
  
  return {
    isValid: missingTypes.length === 0,
    missingTypes,
  };
};