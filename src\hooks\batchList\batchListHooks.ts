// src/hooks/data-ingestion/useGetBatchList.ts
import { useQuery } from "@tanstack/react-query";
import useDataService from "@/services/useDataService";
import { GET_BATCH_LIST_URL } from "../../constants/urls";

interface FileItem {
  file_id: number;
  file_name: string;
  file_format: string;
  upload_status: string;
  import_status: string;
  s3_path: string;
  s3_bucket: string;
}

export interface BatchItem {
  batch_id: number;
  batch_name: string;
  import_status: string;
  upload_status: string;
  total_file_count: number;
  notes: string;
  files: FileItem[];
}

export interface BatchListResponse {
  success: boolean;
  result: {
    data: {
      data: BatchItem[];
      total: number;
      current_page: number;
      page_size: number;
    };
  };
  message: string;
  code: number;
}

export interface BatchListParams {
  page?: number;
  size?: number;
  sort_order?: string;
  search?: string;
  store?: string;
  status?: string;
}

export const useGetBatchList = (params: BatchListParams) => {
  return useQuery({
    queryKey: ["batch-list", params],
    queryFn: async () => {
      const searchParams = new URLSearchParams();
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          searchParams.append(key, String(value));
        }
      });
      const endpoint = `${GET_BATCH_LIST_URL}?${searchParams.toString()}`;
      return await useDataService.getService(endpoint);
    },
    staleTime: 0, // ✅ don’t re-fetch for 5 minutes
  });
};
