import { useQuery } from '@tanstack/react-query';
import useDataService from '@/services/useDataService';
import { GET_VALIDATION_RESULTS_URL } from '@/constants/urls';

export interface ValidationResultParams {
  batch_id: number;
  page?: number;
  size?: number;
}

export interface ValidationRecord {
  [key: string]: any;
}

export interface BatchInfo {
  batch_id: number;
  batch_name: string;
  uploaded_at: string;
}

export interface ValidationResultResponse {
  schema_template_name: string;
  invalid_records: ValidationRecord[];
  batch_info: BatchInfo;
  invalid_rows_count: number;
  valid_rows_count: number;
}

export interface ValidationApiResponse {
  success: boolean;
  message: string;
  code: number;
  result: {
    data: ValidationResultResponse;
    total_items: number;
    total_pages: number;
    current_page: number;
    page_size: number;
  };
}

export const useGetValidationResults = (
  params: ValidationResultParams,
  enabled = true
) => {
  const { batch_id, ...queryParams } = params;

  return useQuery<ValidationApiResponse, Error>({
    queryKey: ['validation-results', params],
    queryFn: async () => {
      const searchParams = new URLSearchParams();
      Object.entries(queryParams).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          searchParams.append(key, String(value));
        }
      });

      const endpoint = `${GET_VALIDATION_RESULTS_URL}/${batch_id}?${searchParams}`;
      return await useDataService.getService(endpoint);
    },
    enabled,
  });
};
