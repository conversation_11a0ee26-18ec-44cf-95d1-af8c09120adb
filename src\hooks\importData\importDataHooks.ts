import { useMutation } from '@tanstack/react-query';
import useDataService from '../../services/useDataService';
import { IMPORTED_DATA_URL } from "@/constants/urls";

// Payload sent to the API
export interface ImportDataPayload {
  table_name: string;
  filters?: any[];
  page?: number;
  size?: number;
  sort_order?: 'asc' | 'desc' | 1 | -1;
}

// Sale record structure
export interface SaleRecord {
  sale_date: string;
  sku_id: string;
  location_id: string;
  quantity: number;
  price_per_unit: number;
  total_amount: number;
}

// API response structure
export interface ImportedDataResponse {
  data: SaleRecord[];
  total_items: number;
  total_pages: number;
  current_page: number;
  page_size: number;
}

// Response wrapper with success/message/code/result
export interface ApiResponse {
  success: boolean;
  message: string;
  code: number;
  result: ImportedDataResponse;
}

export const useImportData = () => {
  return useMutation<ApiResponse, Error, ImportDataPayload>({
    mutationFn: async (data) => {
      return await useDataService.postService(IMPORTED_DATA_URL, data);
    },
  });
};
