import { useMutation } from '@tanstack/react-query';
import useDataService from '../../services/useDataService';
import { STORE_URL } from "@/constants/urls";

// Payload sent to the API
export interface CreateStorePayload {
  location_name: string;
  location_type_id: number;
  status: string;
  country: string;
  state: string;
  city: string;
  street_address: string;
  zip_code: string;
  contact_name: string;
  contact_email: string;
  sku_count: number;
}

type UpdateStorePayload = CreateStorePayload & { location_id: number };

export interface StoreData {
  location_id: number;
  location_name: string;
  location_type_id: number;
  color: string | null;
  contact_name: string;
  contact_email: string;
  sku_count: number;
  created_at: string;
  uploaded_file_id: number | null;
  external_id: string | null;
  address_id: number;
  updated_at: string;
  status: string;
  tenant_id: string;
}

export interface CreateStoreResponse {
  success: boolean;
  result: {
    data: StoreData;
  };
  message: string;
  code: number;
}

export const useCreateStore = () => {
  return useMutation<CreateStoreResponse, Error, CreateStorePayload>({
    mutationFn: async (data) => {
      return await useDataService.postService(STORE_URL, data);
    },
  });
};

export const useUpdateStore = () => {
  return useMutation<CreateStoreResponse, Error, UpdateStorePayload>({
    mutationFn: async (data) => {
      const { location_id, ...payload } = data;
      return await useDataService.patchService(`${STORE_URL}/${location_id}`, payload);
    },
  });
};
