import { useMutation } from '@tanstack/react-query';
import useDataService from '../../services/useDataService';
import { GET_HOLIDAYS_URL } from "@/constants/urls";

export const useGetHolidays = (
  onSuccess?: (data: any) => void,
  onError?: (error: any) => void
) => {
  return useMutation({
    mutationFn: async ({ country, year }: { country: string; year: number }) => {
      const endpoint = `${GET_HOLIDAYS_URL}?country=${country}&year=${year}`;
      const response = await useDataService.getService(endpoint);

      if (!response.success) {
        // Manually throw error so onError gets triggered
        throw response;
      }

      return response;
    },
    onSuccess,
    onError,
  });
};

