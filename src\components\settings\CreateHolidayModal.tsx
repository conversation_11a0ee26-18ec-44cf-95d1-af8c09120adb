import { useState } from "react";
import { useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import * as yup from "yup";
import { X, Calendar, Save, MapPin, TrendingUp } from "lucide-react";

// const schema = yup.object({
//   name: yup.string().required("Holiday name is required"),
//   date: yup.string().required("Date is required"),
//   type: yup.string().required("Holiday type is required"),
//   province: yup.string().when("type", {
//     is: "provincial",
//     then: (schema) =>
//       schema.required("Province is required for provincial holidays"),
//     otherwise: (schema) => schema.notRequired(),
//   }),
//   description: yup.string(),
//   isRecurring: yup.boolean(),
//   affectsForecasting: yup.boolean(),
//   impact: yup.string().when("affectsForecasting", {
//     is: true,
//     then: (schema) =>
//       schema.required("Impact level is required when affects forecasting"),
//     otherwise: (schema) => schema.notRequired(),
//   }),
// });

export const schema = yup.object({
  name: yup.string().required("Holiday name is required"),
  date: yup.string().required("Date is required"),
  type: yup.string().required("Holiday type is required"),
  province: yup.string().when("type", {
    is: "provincial",
    then: (schema) =>
      schema.required("Province is required for provincial holidays"),
    otherwise: (schema) => schema.notRequired(),
  }),
  description: yup.string(), // optional
  isRecurring: yup.boolean().required(),
  affectsForecasting: yup.boolean().required(),
  impact: yup.string().when("affectsForecasting", {
    is: true,
    then: (schema) =>
      schema.required("Impact level is required when affects forecasting"),
    otherwise: (schema) => schema.notRequired(),
  }),
});

export interface CreateHolidayFormData {
  name: string;
  date: string;
  type: string;
  province: string;
  description: string;
  isRecurring: boolean;
  affectsForecasting: boolean;
  impact: string;
}

interface CreateHolidayModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (data: CreateHolidayFormData) => void;
  editingHoliday?: any;
}

export default function CreateHolidayModal({
  isOpen,
  onClose,
  onSubmit,
  editingHoliday,
}: CreateHolidayModalProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);

  const provinces = [
    "Alberta",
    "British Columbia",
    "Manitoba",
    "New Brunswick",
    "Newfoundland and Labrador",
    "Northwest Territories",
    "Nova Scotia",
    "Nunavut",
    "Ontario",
    "Prince Edward Island",
    "Quebec",
    "Saskatchewan",
    "Yukon",
  ];

  const {
    register,
    handleSubmit,
    reset,
    watch,
    formState: { errors },
  } = useForm<CreateHolidayFormData>({
    resolver: yupResolver(schema),
    defaultValues: {
      name: editingHoliday?.name ?? "",
      date: editingHoliday?.date ?? "",
      type: editingHoliday?.type ?? "national",
      province: editingHoliday?.province ?? "",
      description: editingHoliday?.description ?? "",
      isRecurring: editingHoliday?.isRecurring ?? true,
      affectsForecasting: editingHoliday?.affectsForecasting ?? true,
      impact: editingHoliday?.impact ?? "medium",
    },
  });

  const watchType = watch("type");
  const watchAffectsForecasting = watch("affectsForecasting");

  const handleFormSubmit = async (data: CreateHolidayFormData) => {
    setIsSubmitting(true);
    try {
      // Simulate API call
      await new Promise((resolve) => setTimeout(resolve, 1500));
      onSubmit(data);
      reset();
      onClose();
    } catch (error) {
      console.error("Failed to save holiday:", error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleClose = () => {
    reset();
    onClose();
  };

  if (!isOpen) return null;

  return (
    <div
      style={{
        position: "fixed",
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        backgroundColor: "rgba(0, 0, 0, 0.5)",
        display: "flex",
        alignItems: "center",
        justifyContent: "center",
        zIndex: 1000,
      }}
    >
      <div
        style={{
          backgroundColor: "white",
          borderRadius: "0.5rem",
          padding: "2rem",
          maxWidth: "600px",
          width: "90%",
          maxHeight: "90vh",
          overflow: "auto",
          boxShadow:
            "0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)",
        }}
      >
        {/* Header */}
        <div
          style={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
            marginBottom: "2rem",
          }}
        >
          <div
            style={{ display: "flex", alignItems: "center", gap: "0.75rem" }}
          >
            <Calendar size={24} style={{ color: "#2b524f" }} />
            <h2
              style={{
                fontSize: "1.5rem",
                fontWeight: "600",
                color: "#18181b",
                margin: 0,
              }}
            >
              {editingHoliday ? "Edit Holiday" : "Add New Holiday"}
            </h2>
          </div>
          <button
            onClick={handleClose}
            disabled={isSubmitting}
            style={{
              padding: "0.5rem",
              color: "#71717a",
              backgroundColor: "transparent",
              border: "none",
              borderRadius: "0.375rem",
              cursor: isSubmitting ? "not-allowed" : "pointer",
              opacity: isSubmitting ? 0.5 : 1,
              transition: "all 0.2s",
            }}
          >
            <X size={20} />
          </button>
        </div>

        <form onSubmit={handleSubmit(handleFormSubmit)}>
          {/* Basic Information */}
          <div style={{ marginBottom: "2rem" }}>
            <h3
              style={{
                fontSize: "1.125rem",
                fontWeight: "600",
                color: "#18181b",
                marginBottom: "1rem",
              }}
            >
              Basic Information
            </h3>

            <div
              style={{
                display: "grid",
                gridTemplateColumns: "1fr 1fr",
                gap: "1rem",
                marginBottom: "1rem",
              }}
            >
              {/* Holiday Name */}
              <div style={{ gridColumn: "1 / -1" }}>
                <label
                  style={{
                    display: "block",
                    fontSize: "0.875rem",
                    fontWeight: "500",
                    color: "#3f3f46",
                    marginBottom: "0.25rem",
                  }}
                >
                  Holiday Name*
                </label>
                <input
                  {...register("name")}
                  disabled={isSubmitting}
                  style={{
                    width: "100%",
                    padding: "0.625rem 0.75rem",
                    border: `1px solid ${errors.name ? "#f87171" : "#e4e4e7"}`,
                    borderRadius: "0.375rem",
                    backgroundColor: isSubmitting ? "#f9fafb" : "white",
                    color: "#18181b",
                    fontSize: "0.875rem",
                    outline: "none",
                  }}
                  placeholder="Enter holiday name"
                />
                {errors.name && (
                  <p
                    style={{
                      marginTop: "0.25rem",
                      fontSize: "0.75rem",
                      color: "#dc2626",
                    }}
                  >
                    {errors.name.message}
                  </p>
                )}
              </div>

              {/* Date */}
              <div>
                <label
                  style={{
                    display: "block",
                    fontSize: "0.875rem",
                    fontWeight: "500",
                    color: "#3f3f46",
                    marginBottom: "0.25rem",
                  }}
                >
                  Date*
                </label>
                <input
                  {...register("date")}
                  type="date"
                  disabled={isSubmitting}
                  style={{
                    width: "100%",
                    padding: "0.625rem 0.75rem",
                    border: `1px solid ${errors.date ? "#f87171" : "#e4e4e7"}`,
                    borderRadius: "0.375rem",
                    backgroundColor: isSubmitting ? "#f9fafb" : "white",
                    color: "#18181b",
                    fontSize: "0.875rem",
                    outline: "none",
                  }}
                />
                {errors.date && (
                  <p
                    style={{
                      marginTop: "0.25rem",
                      fontSize: "0.75rem",
                      color: "#dc2626",
                    }}
                  >
                    {errors.date.message}
                  </p>
                )}
              </div>

              {/* Holiday Type */}
              <div>
                <label
                  style={{
                    display: "block",
                    fontSize: "0.875rem",
                    fontWeight: "500",
                    color: "#3f3f46",
                    marginBottom: "0.25rem",
                  }}
                >
                  Holiday Type*
                </label>
                <select
                  {...register("type")}
                  disabled={isSubmitting}
                  style={{
                    width: "100%",
                    padding: "0.625rem 0.75rem",
                    border: `1px solid ${errors.type ? "#f87171" : "#e4e4e7"}`,
                    borderRadius: "0.375rem",
                    backgroundColor: isSubmitting ? "#f9fafb" : "white",
                    color: "#18181b",
                    fontSize: "0.875rem",
                    outline: "none",
                  }}
                >
                  <option value="national">National Holiday</option>
                  <option value="provincial">Provincial Holiday</option>
                  <option value="company">Company Holiday</option>
                  <option value="custom">Custom Holiday</option>
                </select>
                {errors.type && (
                  <p
                    style={{
                      marginTop: "0.25rem",
                      fontSize: "0.75rem",
                      color: "#dc2626",
                    }}
                  >
                    {errors.type.message}
                  </p>
                )}
              </div>
            </div>

            {/* Province (conditional) */}
            {watchType === "provincial" && (
              <div style={{ marginBottom: "1rem" }}>
                <label
                  style={{
                    display: "block",
                    fontSize: "0.875rem",
                    fontWeight: "500",
                    color: "#3f3f46",
                    marginBottom: "0.25rem",
                    // display: "flex",
                    alignItems: "center",
                    gap: "0.5rem",
                  }}
                >
                  <MapPin size={14} style={{ color: "#2b524f" }} />
                  Province*
                </label>
                <select
                  {...register("province")}
                  disabled={isSubmitting}
                  style={{
                    width: "100%",
                    padding: "0.625rem 0.75rem",
                    border: `1px solid ${errors.province ? "#f87171" : "#e4e4e7"}`,
                    borderRadius: "0.375rem",
                    backgroundColor: isSubmitting ? "#f9fafb" : "white",
                    color: "#18181b",
                    fontSize: "0.875rem",
                    outline: "none",
                  }}
                >
                  <option value="">Select Province</option>
                  {provinces.map((province) => (
                    <option key={province} value={province}>
                      {province}
                    </option>
                  ))}
                </select>
                {errors.province && (
                  <p
                    style={{
                      marginTop: "0.25rem",
                      fontSize: "0.75rem",
                      color: "#dc2626",
                    }}
                  >
                    {errors.province.message}
                  </p>
                )}
              </div>
            )}

            {/* Description */}
            <div>
              <label
                style={{
                  display: "block",
                  fontSize: "0.875rem",
                  fontWeight: "500",
                  color: "#3f3f46",
                  marginBottom: "0.25rem",
                }}
              >
                Description (Optional)
              </label>
              <textarea
                {...register("description")}
                disabled={isSubmitting}
                rows={3}
                style={{
                  width: "100%",
                  padding: "0.625rem 0.75rem",
                  border: "1px solid #e4e4e7",
                  borderRadius: "0.375rem",
                  backgroundColor: isSubmitting ? "#f9fafb" : "white",
                  color: "#18181b",
                  fontSize: "0.875rem",
                  outline: "none",
                  resize: "vertical",
                }}
                placeholder="Brief description of the holiday..."
              />
            </div>
          </div>

          {/* Holiday Settings */}
          <div style={{ marginBottom: "2rem" }}>
            <h3
              style={{
                fontSize: "1.125rem",
                fontWeight: "600",
                color: "#18181b",
                marginBottom: "1rem",
              }}
            >
              Holiday Settings
            </h3>

            <div>
              {/* Recurring */}
              <div
                style={{
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "space-between",
                  padding: "1rem",
                  backgroundColor: "#f8fafc",
                  border: "1px solid #e2e8f0",
                  borderRadius: "0.375rem",
                  marginBottom: "1rem",
                }}
              >
                <div>
                  <div
                    style={{
                      fontSize: "0.875rem",
                      fontWeight: "500",
                      color: "#18181b",
                    }}
                  >
                    Recurring Holiday
                  </div>
                  <div style={{ fontSize: "0.75rem", color: "#71717a" }}>
                    Holiday occurs annually on the same date
                  </div>
                </div>
                <label
                  style={{
                    display: "flex",
                    alignItems: "center",
                    cursor: "pointer",
                  }}
                >
                  <input
                    {...register("isRecurring")}
                    type="checkbox"
                    disabled={isSubmitting}
                    style={{
                      width: "1rem",
                      height: "1rem",
                      accentColor: "#2b524f",
                      cursor: isSubmitting ? "not-allowed" : "pointer",
                    }}
                  />
                </label>
              </div>

              {/* Affects Forecasting */}
              <div
                style={{
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "space-between",
                  padding: "1rem",
                  backgroundColor: "#f8fafc",
                  border: "1px solid #e2e8f0",
                  borderRadius: "0.375rem",
                  marginBottom: watchAffectsForecasting ? "1rem" : "0",
                }}
              >
                <div>
                  <div
                    style={{
                      fontSize: "0.875rem",
                      fontWeight: "500",
                      color: "#18181b",
                      display: "flex",
                      alignItems: "center",
                      gap: "0.5rem",
                    }}
                  >
                    <TrendingUp size={16} style={{ color: "#2b524f" }} />
                    Affects Forecasting
                  </div>
                  <div style={{ fontSize: "0.75rem", color: "#71717a" }}>
                    Include this holiday in business forecasting models
                  </div>
                </div>
                <label
                  style={{
                    display: "flex",
                    alignItems: "center",
                    cursor: "pointer",
                  }}
                >
                  <input
                    {...register("affectsForecasting")}
                    type="checkbox"
                    disabled={isSubmitting}
                    style={{
                      width: "1rem",
                      height: "1rem",
                      accentColor: "#2b524f",
                      cursor: isSubmitting ? "not-allowed" : "pointer",
                    }}
                  />
                </label>
              </div>

              {/* Impact Level (conditional) */}
              {watchAffectsForecasting && (
                <div
                  style={{
                    padding: "1rem",
                    backgroundColor: "#fef7f0",
                    border: "1px solid #fed7aa",
                    borderRadius: "0.375rem",
                  }}
                >
                  <label
                    style={{
                      display: "block",
                      fontSize: "0.875rem",
                      fontWeight: "500",
                      color: "#18181b",
                      marginBottom: "0.5rem",
                    }}
                  >
                    Forecasting Impact Level*
                  </label>
                  <div
                    style={{
                      display: "grid",
                      gridTemplateColumns: "repeat(3, 1fr)",
                      gap: "0.5rem",
                    }}
                  >
                    {["low", "medium", "high"].map((level) => (
                      <label
                        key={level}
                        style={{
                          display: "flex",
                          alignItems: "center",
                          gap: "0.5rem",
                          cursor: "pointer",
                          padding: "0.5rem",
                          backgroundColor: "white",
                          border: "1px solid #e4e4e7",
                          borderRadius: "0.375rem",
                          transition: "all 0.2s",
                        }}
                      >
                        <input
                          {...register("impact")}
                          type="radio"
                          value={level}
                          disabled={isSubmitting}
                          style={{
                            accentColor: "#2b524f",
                            cursor: isSubmitting ? "not-allowed" : "pointer",
                          }}
                        />
                        <span
                          style={{
                            fontSize: "0.875rem",
                            fontWeight: "500",
                            textTransform: "capitalize",
                            color:
                              level === "high"
                                ? "#dc2626"
                                : level === "medium"
                                  ? "#d97706"
                                  : "#166534",
                          }}
                        >
                          {level}
                        </span>
                      </label>
                    ))}
                  </div>
                  {errors.impact && (
                    <p
                      style={{
                        marginTop: "0.5rem",
                        fontSize: "0.75rem",
                        color: "#dc2626",
                      }}
                    >
                      {errors.impact.message}
                    </p>
                  )}
                  <div
                    style={{
                      fontSize: "0.75rem",
                      color: "#71717a",
                      marginTop: "0.5rem",
                    }}
                  >
                    <strong>Low:</strong> Minimal business impact •{" "}
                    <strong>Medium:</strong> Moderate sales changes •{" "}
                    <strong>High:</strong> Significant traffic/sales impact
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Form Actions */}
          <div
            style={{
              display: "flex",
              gap: "1rem",
              justifyContent: "flex-end",
              borderTop: "1px solid #e4e4e7",
              paddingTop: "1.5rem",
            }}
          >
            <button
              type="button"
              onClick={handleClose}
              disabled={isSubmitting}
              style={{
                backgroundColor: "#e4e4e7",
                color: "#3f3f46",
                borderRadius: "0.375rem",
                padding: "0.625rem 1.5rem",
                fontWeight: "500",
                fontSize: "0.875rem",
                border: "none",
                cursor: isSubmitting ? "not-allowed" : "pointer",
                opacity: isSubmitting ? 0.5 : 1,
              }}
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={isSubmitting}
              style={{
                display: "flex",
                alignItems: "center",
                gap: "0.5rem",
                backgroundColor: isSubmitting ? "#d1d5db" : "#2b524f",
                color: "white",
                borderRadius: "0.375rem",
                padding: "0.625rem 1.5rem",
                fontWeight: "500",
                fontSize: "0.875rem",
                border: "none",
                cursor: isSubmitting ? "not-allowed" : "pointer",
              }}
            >
              {isSubmitting ? (
                <>
                  <div
                    style={{
                      width: "1rem",
                      height: "1rem",
                      border: "2px solid rgba(255, 255, 255, 0.3)",
                      borderTop: "2px solid white",
                      borderRadius: "50%",
                      animation: "spin 1s linear infinite",
                    }}
                  />
                  {editingHoliday ? "Updating..." : "Creating..."}
                </>
              ) : (
                <>
                  <Save size={14} />
                  {editingHoliday ? "Update Holiday" : "Create Holiday"}
                </>
              )}
            </button>
          </div>
        </form>

        <style>{`
          @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
          }
        `}</style>
      </div>
    </div>
  );
}
