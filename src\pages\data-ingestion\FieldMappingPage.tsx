import { useState, useEffect, useMemo } from "react";
import { useParams, useNavigate } from "react-router-dom";
import {
  ArrowLeft,
  FileText,
  Database,
  Check,
  X,
  Eye,
  Save,
  ArrowRight,
  Table,
  Columns,
  AlertCircle,
  CheckCircle,
  Minus,
  Sparkles,
  Loader2,
  AlertTriangle,
  TableCellsSplit,
  Sheet,
  ChevronDown,
} from "lucide-react";
import { useToast } from "@/hooks/useToast";
import { ToastContainer } from "@/components/common/Toast";
import ProcessStepper from "@/components/data-ingestion/ProcessStepper";
import { Card, CardHeader, CardTitle, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import {
  Select,
  SelectTrigger,
  SelectValue,
  SelectContent,
  SelectItem,
} from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogClose,
} from "@/components/ui/dialog";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { useGetFlashanaSchema } from "@/hooks/fieldMapping/flashanaSchemaHooks";
import { useGetSchemaTemplate } from "@/hooks/fieldMapping/schemaTemplateHooks";

interface TableSchema {
  id: string;
  name: string;
  description: string;
  fields: SchemaField[];
}

interface SchemaField {
  id: string;
  name: string;
  type: string;
  description: string;
  isRequired: boolean;
  sampleData?: string[];
}

interface FieldMapping {
  sourceFieldId: string;
  targetFieldId: string;
  tableId: string;
}

interface TableStatus {
  tableId: string;
  requiredFields: number;
  mappedFields: number;
  isComplete: boolean;
}

interface FieldMappingPageProps {
  handleStepper: (show: boolean, step: number) => void;
  selectedBatchId?: string | null;
}

export default function FieldMappingPage({
  handleStepper,
  selectedBatchId,
}: FieldMappingPageProps) {
  const { batchId } = useParams();
  const navigate = useNavigate();
  const { toasts, removeToast, success, error, warning } = useToast();

  const [flashanaTables] = useState<TableSchema[]>([
    {
      id: "sales",
      name: "Sales Transactions",
      description: "Primary sales transaction data",
      fields: [
        {
          id: "sales_sku",
          name: "sku",
          type: "string",
          description: "Product SKU identifier",
          isRequired: true,
        },
        {
          id: "sales_title",
          name: "product_title",
          type: "string",
          description: "Product display name",
          isRequired: true,
        },
        {
          id: "sales_price",
          name: "unit_price",
          type: "decimal",
          description: "Price per unit in CAD",
          isRequired: true,
        },
        {
          id: "sales_qty",
          name: "units_sold",
          type: "integer",
          description: "Number of units sold",
          isRequired: true,
        },
        {
          id: "sales_date",
          name: "transaction_date",
          type: "datetime",
          description: "Date and time of transaction",
          isRequired: true,
        },
        {
          id: "sales_customer",
          name: "customer_id",
          type: "string",
          description: "Customer identifier",
          isRequired: false,
        },
        {
          id: "sales_discount",
          name: "discount_amount",
          type: "decimal",
          description: "Discount amount in CAD",
          isRequired: false,
        },
        {
          id: "sales_store",
          name: "store_code",
          type: "string",
          description: "Store location code",
          isRequired: false,
        },
      ],
    },
    {
      id: "products",
      name: "Product Catalog",
      description: "Product information and details",
      fields: [
        {
          id: "prod_sku",
          name: "sku",
          type: "string",
          description: "Product SKU identifier",
          isRequired: true,
        },
        {
          id: "prod_name",
          name: "product_name",
          type: "string",
          description: "Product name",
          isRequired: true,
        },
        {
          id: "prod_category",
          name: "category",
          type: "string",
          description: "Product category",
          isRequired: true,
        },
        {
          id: "prod_brand",
          name: "brand",
          type: "string",
          description: "Product brand",
          isRequired: false,
        },
        {
          id: "prod_cost",
          name: "cost_price",
          type: "decimal",
          description: "Product cost price",
          isRequired: false,
        },
        {
          id: "prod_retail",
          name: "retail_price",
          type: "decimal",
          description: "Retail price",
          isRequired: true,
        },
      ],
    },
    {
      id: "customers",
      name: "Customer Information",
      description: "Customer demographics and contact details",
      fields: [
        {
          id: "cust_id",
          name: "customer_id",
          type: "string",
          description: "Unique customer identifier",
          isRequired: true,
        },
        {
          id: "cust_email",
          name: "email",
          type: "string",
          description: "Customer email address",
          isRequired: true,
        },
        {
          id: "cust_fname",
          name: "first_name",
          type: "string",
          description: "Customer first name",
          isRequired: false,
        },
        {
          id: "cust_lname",
          name: "last_name",
          type: "string",
          description: "Customer last name",
          isRequired: false,
        },
        {
          id: "cust_phone",
          name: "phone",
          type: "string",
          description: "Customer phone number",
          isRequired: false,
        },
        {
          id: "cust_addr",
          name: "address",
          type: "string",
          description: "Customer address",
          isRequired: false,
        },
      ],
    },
  ]);

  // const [selectedFlashanaTable, setSelectedFlashanaTable] =
  //   useState<string>("");
  // const [selectedUploadedFile, setSelectedUploadedFile] = useState<string>("");
  const [mappings, setMappings] = useState<FieldMapping[]>([]);
  const [draggedField, setDraggedField] = useState<string | null>(null);
  const [showPreview, setShowPreview] = useState(false);
  const [isAutoMapping, setIsAutoMapping] = useState(false);

  const [selectedFlashanaTable] = useState<string>("sales");

  const getCurrentFlashanaTable = () =>
    flashanaTables.find((t) => t.id === selectedFlashanaTable);

  const handleDragStart = (fieldId: string) => {
    setDraggedField(fieldId);
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
  };

  const handleDrop = (e: React.DragEvent, targetFieldId: string) => {
    e.preventDefault();
    if (draggedField && selectedFlashanaTable) {
      // Remove existing mapping for this target field
      const newMappings = mappings.filter(
        (m) => m.targetFieldId !== targetFieldId
      );
      // Add new mapping
      newMappings.push({
        sourceFieldId: draggedField,
        targetFieldId,
        tableId: selectedFlashanaTable,
      });
      setMappings(newMappings);
      setDraggedField(null);
    }
  };

  const removeMapping = (targetFieldId: string) => {
    setMappings(mappings.filter((m) => m.targetFieldId !== targetFieldId));
  };

  const getMappingForTarget = (targetFieldId: string) =>
    mappings.find(
      (m) =>
        m.targetFieldId === targetFieldId && m.tableId === selectedFlashanaTable
    );

  const getSourceFieldById = (fieldId: string) => {
    const currentFile = getCurrentUploadedFile();
    return currentFile?.fields.find((f) => f.id === fieldId);
  };

  const handleSaveMapping = () => {
    console.log("Saving mappings:", mappings);
    success(
      "Mappings saved successfully!",
      "Field mappings have been saved to the system."
    );
  };

  const handlePreview = () => {
    if (!selectedFlashanaTable || !selectedUploadedFile) {
      error(
        "Missing Selection",
        "Please select both Flashana table and uploaded file first."
      );
      return;
    }
    setShowPreview(true);
  };

  const handleAutoMap = async () => {
    if (!selectedFlashanaTable || !selectedUploadedFile) {
      error(
        "Missing Selection",
        "Please select both Flashana table and uploaded file first."
      );
      return;
    }

    setIsAutoMapping(true);

    // Simulate AI processing time
    await new Promise((resolve) => setTimeout(resolve, 2000));

    const currentTable = getCurrentFlashanaTable();
    const currentFile = getCurrentUploadedFile();

    if (!currentTable || !currentFile) {
      setIsAutoMapping(false);
      return;
    }

    // AI-based mapping logic
    const newMappings: FieldMapping[] = [];

    // Create a mapping between common field name patterns
    const mappingRules: Record<string, string[]> = {
      sku: ["sku", "product_id", "item_id", "product_code"],
      product_title: [
        "product_name",
        "product_title",
        "name",
        "title",
        "item_name",
      ],
      unit_price: ["price", "unit_price", "cost", "amount", "retail_price"],
      units_sold: [
        "quantity",
        "qty",
        "units_sold",
        "quantity_sold",
        "amount_sold",
      ],
      transaction_date: [
        "date",
        "sale_date",
        "transaction_date",
        "purchase_date",
        "created_at",
      ],
      customer_id: [
        "customer_id",
        "customer_email",
        "customer",
        "buyer_id",
        "user_id",
      ],
      discount_amount: [
        "discount",
        "discount_amount",
        "discount_percent",
        "reduction",
      ],
      store_code: [
        "store",
        "store_code",
        "store_location",
        "location",
        "branch",
      ],
      product_name: ["product_name", "name", "title", "item_name"],
      category: ["category", "product_category", "type", "classification"],
      brand: ["brand", "manufacturer", "company", "brand_name"],
      cost_price: ["cost", "cost_price", "wholesale_price", "purchase_price"],
      retail_price: ["retail", "retail_price", "selling_price", "price"],
      email: ["email", "customer_email", "contact_email", "user_email"],
      first_name: ["first_name", "fname", "given_name", "firstname"],
      last_name: ["last_name", "lname", "surname", "lastname"],
      phone: ["phone", "telephone", "mobile", "contact_number"],
      address: ["address", "location", "street_address", "addr"],
    };

    // Auto-map fields based on similarity
    currentTable.fields.forEach((targetField) => {
      const targetFieldName = targetField.name.toLowerCase();
      const possibleMatches = mappingRules[targetFieldName] || [
        targetFieldName,
      ];

      // Find best match from source fields
      const sourceField = currentFile.fields.find((field) => {
        const sourceFieldName = field.name.toLowerCase();
        return possibleMatches.some(
          (pattern) =>
            sourceFieldName.includes(pattern) ||
            pattern.includes(sourceFieldName)
        );
      });

      if (
        sourceField &&
        !newMappings.some((m) => m.sourceFieldId === sourceField.id)
      ) {
        newMappings.push({
          sourceFieldId: sourceField.id,
          targetFieldId: targetField.id,
          tableId: selectedFlashanaTable,
        });
      }
    });

    // Update mappings for current table
    const otherMappings = mappings.filter(
      (m) => m.tableId !== selectedFlashanaTable
    );
    setMappings([...otherMappings, ...newMappings]);

    setIsAutoMapping(false);

    // Show success message
    const mappedCount = newMappings.length;
    const requiredCount = currentTable.fields.filter(
      (f) => f.isRequired
    ).length;

    if (mappedCount >= requiredCount) {
      success(
        "AI Mapping Complete!",
        `Successfully mapped ${mappedCount} fields. All required fields have been mapped.`
      );
    } else {
      warning(
        "AI Mapping Partial",
        `Mapped ${mappedCount} fields. Please manually map the remaining ${requiredCount - mappedCount} required fields.`
      );
    }
  };

  const handleContinueToValidation = () => {
    const overallStatus = getOverallStatus();
    if (overallStatus.isAllComplete) {
      navigate(`/data-ingestion/validation/${batchId}`);
    } else {
      error(
        "Mapping Incomplete",
        "Please map all required fields in all tables before continuing to validation."
      );
    }
  };

  // API call to get flashana schema
  const {
    data: flashanaData,
    isLoading: isFlashanaSchemaLoading,
    error: errorFlashanaSchema,
  } = useGetFlashanaSchema(Number(selectedBatchId));

  // API call to get schema template (uploaded file schema)
  const {
    data: schemaTemplateData,
    isLoading: isSchemaTemplateLoading,
    error: errorSchemaTemplate,
  } = useGetSchemaTemplate(Number(selectedBatchId));

  // Show loading state
  if (isFlashanaSchemaLoading || isSchemaTemplateLoading) {
    return (
      <div className="h-[100vh] flex items-center justify-center">
        <div className="flex items-center gap-2">
          <Loader2 size={20} className="animate-spin" />
          <span>Loading schema data...</span>
        </div>
      </div>
    );
  }

  // Show error state
  if (errorFlashanaSchema || errorSchemaTemplate) {
    console.error("Flashana Schema Error:", errorFlashanaSchema);
    console.error("Schema Template Error:", errorSchemaTemplate);

    return (
      <div className="h-[100vh] flex items-center justify-center">
        <div className="text-center max-w-md">
          <AlertCircle size={48} className="mx-auto text-red-500 mb-4" />
          <h2 className="text-xl font-semibold mb-2">Error Loading Schema</h2>
          <div className="text-gray-600 space-y-2">
            {errorFlashanaSchema && (
              <div className="p-3 bg-red-50 border border-red-200 rounded">
                <p className="font-medium text-red-800">
                  Flashana Schema Error:
                </p>
                <p className="text-sm text-red-600">
                  {errorFlashanaSchema?.message ||
                    JSON.stringify(errorFlashanaSchema)}
                </p>
              </div>
            )}
            {errorSchemaTemplate && (
              <div className="p-3 bg-red-50 border border-red-200 rounded">
                <p className="font-medium text-red-800">
                  Schema Template Error:
                </p>
                <p className="text-sm text-red-600">
                  {errorSchemaTemplate?.message ||
                    JSON.stringify(errorSchemaTemplate)}
                </p>
              </div>
            )}
            <button
              onClick={() => window.location.reload()}
              className="mt-4 px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
            >
              Retry
            </button>
          </div>
        </div>
      </div>
    );
  }

  // Destructure values safely from get flashana schema API response
  const schemas = flashanaData?.result.data ?? [];
  console.log("Flashana Schema", schemas);
  console.log("Schema Template Data", schemaTemplateData);

  // Transform schema template data to match the expected structure
  const uploadedFiles = useMemo(() => {
    if (!schemaTemplateData?.data) return [];

    return schemaTemplateData.data.map((template: any) => ({
      id: String(template.schema_template_id),
      name: template.schema_template_name,
      fields: template.columns.map((col: any) => ({
        id: String(col.schema_template_column_id),
        name: col.schema_template_column_name,
        type: col.data_type,
        isRequired: col.is_required,
        isMapped: col.is_mapped,
        sampleData: [], // Sample data not provided by API
      })),
    }));
  }, [schemaTemplateData]);

  // Use the first uploaded file from API data, fallback to hardcoded value
  const selectedUploadedFile = useMemo(() => {
    return uploadedFiles.length > 0 ? String(uploadedFiles[0].id) : "file1";
  }, [uploadedFiles]);

  const getCurrentUploadedFile = () =>
    uploadedFiles.find((f: any) => f.id === selectedUploadedFile);

  const tables = useMemo(() => {
    return (flashanaData?.result?.data || []).map((table: any) => ({
      id: table.flashana_schema_id,
      name: table.flashana_schema_name,
      fields: table.columns.map((col: any) => ({
        id: col.flashana_schema_column_id,
        name: col.flashana_schema_column_name,
        isMapped: col.is_mapped,
        isRequired: col.is_required,
        mappedFields: (col.mapped_fields || []).map((mf: any) => ({
          id: mf.schema_template_column_id,
          name: mf.schema_template_column_name,
        })),
      })),
    }));
  }, [flashanaData]);

  const getTableStatuses = (): TableStatus[] => {
    // Use API data if available, otherwise fall back to hardcoded data
    const tablesToCheck = tables.length > 0 ? tables : flashanaTables;

    return tablesToCheck.map((table: any) => {
      const requiredFields = table.fields.filter(
        (f: any) => f.isRequired
      ).length;

      // Count both API mapped fields and local mappings
      let mappedFields = 0;

      if (tables.length > 0) {
        // For API data, count fields that are marked as mapped
        mappedFields = table.fields.filter(
          (f: any) => f.isRequired && f.isMapped
        ).length;
      } else {
        // For hardcoded data, count local mappings
        mappedFields = mappings.filter(
          (m) =>
            m.tableId === table.id &&
            table.fields.find((f: any) => f.id === m.targetFieldId)?.isRequired
        ).length;
      }

      return {
        tableId: String(table.id),
        requiredFields,
        mappedFields,
        isComplete: mappedFields === requiredFields,
      };
    });
  };

  const getOverallStatus = () => {
    const statuses = getTableStatuses();
    const completeTables = statuses.filter((s) => s.isComplete).length;
    const totalRequiredMappings = statuses.reduce(
      (sum, s) => sum + s.requiredFields,
      0
    );
    const totalMappedRequired = statuses.reduce(
      (sum, s) => sum + s.mappedFields,
      0
    );

    // Use API data count if available, otherwise use hardcoded data
    const totalTables =
      tables.length > 0 ? tables.length : flashanaTables.length;

    return {
      completeTables,
      totalTables,
      totalRequiredMappings,
      totalMappedRequired,
      isAllComplete: completeTables === totalTables,
      statusText:
        completeTables === 0
          ? "Not Mapped"
          : completeTables === totalTables
            ? "Mapped"
            : "Partially Mapped",
    };
  };

  const currentSchema = tables?.[0];
  const overallStatus = getOverallStatus();
  const tableStatuses = getTableStatuses();
  const currentTable = getCurrentFlashanaTable();
  const currentFile = getCurrentUploadedFile();

  return (
    <div className="h-[100vh]">
      {/* Header */}
      <Card className="mb-4">
        <CardHeader className="p-4 border-0">
          <div className="flex items-center justify-between gap-4">
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                size="icon"
                onClick={() => handleStepper(false, 1)}
                className="border-none w-6 h-6"
              >
                <ArrowLeft size={16} />
              </Button>
              <div>
                <div className="text-sm text-zinc-600">Batch Name</div>
                <span className="text-zinc-900 font-semibold">
                  Jan_Batch_2025
                </span>
              </div>
            </div>
            {/* Auto-map Button */}
            {selectedFlashanaTable && selectedUploadedFile && (
              <div className="flex justify-center">
                <Button
                  onClick={handleAutoMap}
                  disabled={isAutoMapping}
                  className={isAutoMapping ? "bg-muted-400" : "bg-primary"}
                >
                  {isAutoMapping ? (
                    <>
                      <Loader2 size={16} className="animate-spin" />
                      Mapping with AI...
                    </>
                  ) : (
                    <>
                      <Sparkles size={16} className="me-2" />
                      Auto-map AI
                    </>
                  )}
                </Button>
              </div>
            )}
          </div>
        </CardHeader>
      </Card>
      {/* Main Layout */}
      <div className="grid grid-cols-1 xl:grid-cols-[3fr_1fr] gap-4 sticky top-20 max-h-[calc(100%_-_160px)]">
        {/* Schema Mapping Section */}
        <Card className="max-h-[calc(100vh_-_140px)]">
          <CardContent className="p-4 h-[calc(100%_-_70px)]">
            {/* Field Mapping Interface */}
            {selectedFlashanaTable && selectedUploadedFile && (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 h-full">
                {/* Flashana Table Fields */}
                <div className="border rounded-lg overflow-auto">
                  <h1 className="text-lg font-semibold p-3.5 border-b">
                    Flashana Tables
                  </h1>
                  <div className="p-3.5">
                    <div className="flex items-center gap-2 mb-4">
                      <h3 className="text-base font-semibold text-muted-900 m-0">
                        <span className="text-zinc-600 font-normal">
                          Table Name :{" "}
                        </span>
                        {currentSchema?.name ?? "No table selected"}
                      </h3>
                    </div>
                    <div>
                      {currentSchema?.fields.map((field) => {
                        const mapping = getMappingForTarget(String(field.id));
                        const sourceField = mapping
                          ? getSourceFieldById(mapping.sourceFieldId)
                          : null;

                        // Check if field is mapped from API response
                        const isApiMapped = field.isMapped;
                        const apiMappedFields = field.mappedFields || [];

                        return (
                          <Card
                            key={field.id}
                            className="mb-4 shadow-none border-none bg-zinc-50"
                          >
                            <CardContent className="p-3">
                              <div className="flex justify-between items-center mb-[10px]">
                                <div className="w-full flex items-center justify-between gap-2">
                                  <span className="font-semibold text-muted-900">
                                    {field.name}
                                  </span>
                                  <Button
                                    variant="outline"
                                    className={`font-normal px-[8px] h-[24px] rounded-[3px] ${
                                      isApiMapped || mapping
                                        ? "border-green-600 text-green-600 bg-green-50"
                                        : "border-zinc-500 text-zinc-500"
                                    }`}
                                  >
                                    {isApiMapped || mapping
                                      ? "Mapped"
                                      : "Not Mapped"}
                                  </Button>
                                </div>
                              </div>

                              {/* Show API mapped fields */}
                              {isApiMapped && apiMappedFields.length > 0 ? (
                                <div className="space-y-2">
                                  {apiMappedFields.map((mappedField) => (
                                    <div
                                      key={mappedField.id}
                                      className="flex justify-between bg-green-50 p-3 rounded border border-green-200 text-xs"
                                    >
                                      <div className="text-[12px] font-medium text-green-700">
                                        Mapped to:{" "}
                                        <span className="text-green-800 font-semibold">
                                          "{mappedField.name}"
                                        </span>
                                      </div>
                                      <div className="flex items-center gap-2">
                                        <Check
                                          size={16}
                                          className="text-green-600"
                                        />
                                      </div>
                                    </div>
                                  ))}
                                </div>
                              ) : mapping && sourceField ? (
                                /* Show local mapping */
                                <div className="flex justify-between bg-white p-3 rounded border text-xs">
                                  <div className="text-[12px] font-medium text-zinc-600">
                                    Mapped to:{" "}
                                    <span className="text-zinc-800 font-semibold">
                                      "{sourceField.name}"
                                    </span>
                                  </div>
                                  <div className="flex items-center gap-2">
                                    {mapping && (
                                      <Button
                                        variant="ghost"
                                        onClick={() =>
                                          removeMapping(String(field.id))
                                        }
                                        className="p-0 h-[8px]"
                                      >
                                        <X size={16} />
                                      </Button>
                                    )}
                                  </div>
                                </div>
                              ) : (
                                /* Show drag and drop area for unmapped fields */
                                <div
                                  className={`bg-white mb-3 transition-all rounded-md border-2 ${mapping ? "border-green-900 bg-green-50" : "border-dashed border-muted-200 bg-muted-50"}`}
                                  onDragOver={handleDragOver}
                                  onDrop={(e) =>
                                    handleDrop(e, String(field.id))
                                  }
                                >
                                  <div className="text-center text-zinc-600 text-sm p-2">
                                    Drag and Drop
                                  </div>
                                </div>
                              )}
                            </CardContent>
                          </Card>
                        );
                      })}
                    </div>
                  </div>
                </div>
                {/* Uploaded File Fields */}
                <div className="border rounded-lg overflow-auto">
                  <h1 className="text-lg font-semibold p-3.5 border-b">
                    Uploaded File Schema
                  </h1>
                  <div className="p-3.5">
                    <div className="flex items-center gap-2 mb-4">
                      <h3 className="text-base font-semibold text-muted-900 m-0">
                        <span className="text-zinc-600 font-normal">
                          Schema Name :{" "}
                        </span>
                        {currentFile?.name}
                      </h3>
                    </div>
                    <div>
                      {currentFile?.fields.map((field) => (
                        <Card
                          key={field.id}
                          draggable
                          onDragStart={() => handleDragStart(field.id)}
                          className="cursor-move transition-all mb-4 rounded shadow-none bg-zinc-50 hover:rounded"
                        >
                          <CardContent className="p-3 hover:shadow hover:border-yellow-700">
                            <div className="flex justify-between items-center">
                              <span className="font-regular text-sm text-muted-900">
                                {field.name}
                              </span>
                            </div>
                          </CardContent>
                        </Card>
                      ))}
                    </div>
                  </div>
                </div>
              </div>
            )}
          </CardContent>
          {/* Action Buttons */}
          {selectedFlashanaTable && selectedUploadedFile && (
            <div className="flex justify-end gap-4 p-4 border-t">
              <Button onClick={handlePreview} variant="outline">
                Preview
              </Button>
              <Button onClick={handleSaveMapping} className="bg-primary">
                Save Mapping
              </Button>
            </div>
          )}
        </Card>

        {/* Status Section */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <span>Status</span>
              <div
                className={`
              rounded-md px-2 py-1 font-medium text-sm whitespace-nowrap
              ${
                overallStatus.statusText === "Validated"
                  ? "border border-green-700 text-green-700"
                  : overallStatus.statusText === "Partially Validated"
                    ? "border border-yellow-600 text-yellow-600"
                    : "border border-rose-700 text-rose-700"
              }
            `}
              >
                {overallStatus.statusText}
              </div>
            </CardTitle>
          </CardHeader>

          <CardContent className="p-0">
            <Accordion
              collapsible
              type="multiple"
              className="w-full"
              defaultValue={(tables.length > 0 ? tables : flashanaTables).map(
                (table) => String(table.id)
              )}
            >
              {(tables.length > 0 ? tables : flashanaTables).map((table) => {
                const status = tableStatuses.find(
                  (s) => s.tableId === String(table.id)
                );
                if (!status) return null;

                return (
                  <AccordionItem
                    key={table.id}
                    value={String(table.id)}
                    className={`${status.isComplete ? "bg-green-50" : "bg-muted-50"}`}
                  >
                    <AccordionTrigger className="px-2 py-2 text-left bg-transparent hover:bg-transparent relative group">
                      <div className="flex items-center gap-2">
                        <ChevronDown
                          size={16}
                          className="text-zinc-400 transition-transform duration-200 group-[.open]:rotate-180"
                        />
                        <Table size={16} className="text-primary" />
                        <span className="font-medium text-muted-900 text-base">
                          {table.name}
                        </span>
                      </div>
                      <style>{`.group .lucide-chevron-down:last-child { display: none !important; }`}</style>
                    </AccordionTrigger>

                    <AccordionContent className="px-4 border-none">
                      {table.fields
                        .filter((f: any) => f.isRequired)
                        .map((field: any) => {
                          // For API data, check isMapped; for local data, check mappings
                          const isFieldMapped =
                            tables.length > 0
                              ? field.isMapped
                              : mappings.find(
                                  (m) =>
                                    m.targetFieldId === field.id &&
                                    m.tableId === table.id
                                );

                          return (
                            <div
                              key={field.id}
                              className="status-submenu border-l flex justify-between items-center text-xs px-4 py-2"
                            >
                              <div className="w-full flex justify-between items-center gap-1">
                                <span className="flex items-center gap-2 text-sm text-zinc-800">
                                  <TableCellsSplit
                                    size={15}
                                    className="text-primary"
                                  />
                                  {field.name}
                                </span>
                                {isFieldMapped ? (
                                  <Check
                                    size={16}
                                    className="text-emerald-700 ml-auto"
                                  />
                                ) : (
                                  <AlertTriangle
                                    size={16}
                                    className="text-rose-700 ml-auto"
                                  />
                                )}
                              </div>
                            </div>
                          );
                        })}
                    </AccordionContent>
                  </AccordionItem>
                );
              })}
            </Accordion>

            <Button
              onClick={handleContinueToValidation}
              disabled={!overallStatus.isAllComplete}
              className={`w-full mt-4 flex justify-center ${
                overallStatus.isAllComplete ? "bg-green-900" : "bg-muted-400"
              }`}
            >
              Continue to Validation
              <ArrowRight size={14} />
            </Button>
          </CardContent>
        </Card>
      </div>

      {/* Preview Modal */}
      <Dialog open={showPreview} onOpenChange={setShowPreview}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Preview: {currentTable?.name}</DialogTitle>
            <DialogClose asChild>
              <Button
                variant="ghost"
                size="icon"
                className="absolute right-4 top-4"
                onClick={() => setShowPreview(false)}
              >
                <X size={20} />
              </Button>
            </DialogClose>
          </DialogHeader>
          <div className="bg-muted-50 border rounded p-4 mb-4">
            <div className="font-semibold text-muted-900 mb-1">
              Sample Mapped Data (Top 5 rows):
            </div>
            <div className="text-xs text-muted-500">
              Preview shows how your mapped fields will appear in the Flashana{" "}
              {currentTable?.name} table
            </div>
          </div>
          <div className="overflow-x-auto">
            <table className="w-full text-sm border">
              <thead>
                <tr className="bg-muted-50 border-b">
                  {currentTable?.fields
                    .filter((f) =>
                      mappings.some(
                        (m) =>
                          m.targetFieldId === f.id &&
                          m.tableId === currentTable.id
                      )
                    )
                    .map((field) => (
                      <th
                        key={field.id}
                        className="text-left p-3 text-muted-900 font-semibold border-r"
                      >
                        {field.name}
                        <div className="text-xs font-normal text-muted-500">
                          {field.type}
                        </div>
                      </th>
                    ))}
                </tr>
              </thead>
              <tbody>
                {[0, 1, 2, 3, 4].map((rowIndex) => (
                  <tr key={rowIndex} className="border-b">
                    {currentTable?.fields
                      .filter((f) =>
                        mappings.some(
                          (m) =>
                            m.targetFieldId === f.id &&
                            m.tableId === currentTable.id
                        )
                      )
                      .map((field) => {
                        const mapping = mappings.find(
                          (m) =>
                            m.targetFieldId === field.id &&
                            m.tableId === currentTable.id
                        );
                        const sourceField = mapping
                          ? getSourceFieldById(mapping.sourceFieldId)
                          : null;
                        const sampleValue =
                          sourceField?.sampleData?.[
                            rowIndex % (sourceField.sampleData?.length || 1)
                          ] || "N/A";
                        return (
                          <td
                            key={field.id}
                            className="p-3 text-muted-900 border-r"
                          >
                            {sampleValue}
                          </td>
                        );
                      })}
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
          <div className="flex gap-4 justify-end mt-6">
            <Button variant="secondary" onClick={() => setShowPreview(false)}>
              Close
            </Button>
          </div>
        </DialogContent>
      </Dialog>

      {/* Toast Notifications */}
      <ToastContainer toasts={toasts} onClose={removeToast} />
    </div>
  );
}
