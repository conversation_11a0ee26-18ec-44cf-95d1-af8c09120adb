import { motion } from "framer-motion";
import { cn } from "@/lib/utils";

interface FlashanaLogoProps {
  variant?: "full" | "icon" | "text";
  size?: "small" | "medium" | "large";
  color?: "default" | "white" | "primary";
  onClick?: () => void;
  animated?: boolean;
  className?: string;
}

export default function FlashanaLogo({
  variant = "full",
  size = "medium",
  color = "default",
  onClick,
  animated = true,
  className,
}: FlashanaLogoProps) {
  const getSizeStyles = () => {
    switch (size) {
      case "small":
        return { height: 24, fontSize: "1.25rem" };
      case "large":
        return { height: 48, fontSize: "2.5rem" };
      default:
        return { height: 32, fontSize: "1.75rem" };
    }
  };

  const getTextColor = () => {
    switch (color) {
      case "white":
        return "text-white";
      case "primary":
        return "text-primary";
      default:
        return "text-foreground";
    }
  };

  const sizeStyles = getSizeStyles();

  const logoVariants = {
    initial: {
      opacity: 0,
      scale: 0.9,
    },
    animate: {
      opacity: 1,
      scale: 1,
      transition: {
        duration: 0.3,
        ease: "easeInOut" as const, // ✅ TS accepts this
      },
    },
    hover: {
      scale: 1.1,
      transition: {
        duration: 0.2,
        ease: "easeInOut" as const, // ✅ or use a tuple like [0.42, 0, 0.58, 1]
      },
    },
  };

  if (variant === "text") {
    const TextComponent = animated ? motion.span : "span";
    return (
      <TextComponent
        onClick={onClick}
        variants={animated ? logoVariants : undefined}
        initial={animated ? "initial" : undefined}
        animate={animated ? "animate" : undefined}
        whileHover={animated && onClick ? "hover" : undefined}
        className={cn(
          "font-bold tracking-tight font-roboto cursor-pointer select-none",
          getTextColor(),
          onClick && "cursor-pointer",
          className
        )}
        style={{ fontSize: sizeStyles.fontSize }}
      >
        flashana
      </TextComponent>
    );
  }

  // Full or icon variant - just show the logo image
  const ContainerComponent = animated ? motion.div : "div";

  return (
    <ContainerComponent
      onClick={onClick}
      variants={animated ? logoVariants : undefined}
      initial={animated ? "initial" : undefined}
      animate={animated ? "animate" : undefined}
      whileHover={animated && onClick ? "hover" : undefined}
      className={cn(
        "flex items-center justify-center",
        onClick && "cursor-pointer",
        className
      )}
    >
      <motion.img
        src="/flashana-logo.png"
        alt="Flashana"
        style={{ height: sizeStyles.height, width: "auto" }}
        className="object-contain"
        whileHover={animated && onClick ? { rotate: [0, -5, 5, 0] } : undefined}
        transition={{ duration: 0.5 }}
      />
    </ContainerComponent>
  );
}
