interface SalesDataRow {
  sale_date: string;
  sku_id: string;
  quantity: number | null;
  sales_amount: string;
  hasError?: boolean;
  errorType?: 'missing' | 'invalid';
}

interface LocationRow {
  location_id: string;
  location_name: string;
  region: string;
  country: string;
  hasError?: boolean;
  errorType?: 'missing' | 'invalid';
}

interface SkuRow {
  sku_id: string;
  product_name: string;
  category: string;
  price: string;
  hasError?: boolean;
  errorType?: 'missing' | 'invalid' | 'duplicate';
}

export const salesMockData: SalesDataRow[] = [
  { sale_date: '12-05-2024', sku_id: 'iPhone 15 Pro', quantity: 318, sales_amount: '$1,999.00', hasError: false },
  { sale_date: '08-02-2025', sku_id: 'Mac Book Air', quantity: 245, sales_amount: '', hasError: true, errorType: 'missing' },
  { sale_date: '04-25-2025', sku_id: 'iPad Pro', quantity: null, sales_amount: '$3,297.00', hasError: true, errorType: 'invalid' },
  { sale_date: '01-15-2025', sku_id: 'AirPods Pro', quantity: 567, sales_amount: '$249.00', hasError: false },
  { sale_date: '03-20-2025', sku_id: 'Apple Watch', quantity: 189, sales_amount: '$399.00', hasError: false },
  { sale_date: '02-10-2025', sku_id: 'Mac Studio', quantity: 45, sales_amount: '$1,999.00', hasError: false },
  { sale_date: '05-05-2025', sku_id: 'iPhone 14', quantity: 234, sales_amount: '$799.00', hasError: false },
  { sale_date: '06-12-2025', sku_id: 'iPad Air', quantity: 156, sales_amount: '$599.00', hasError: false },
  { sale_date: '07-08-2025', sku_id: 'HomePod', quantity: 89, sales_amount: '$299.00', hasError: false },
  { sale_date: '08-15-2025', sku_id: 'Apple TV', quantity: 234, sales_amount: '$149.00', hasError: false },
  { sale_date: '09-01-2025', sku_id: 'Magic Keyboard', quantity: 78, sales_amount: '$349.00', hasError: false },
  { sale_date: '09-05-2025', sku_id: 'Magic Mouse', quantity: 145, sales_amount: '$99.00', hasError: false },
  { sale_date: '09-10-2025', sku_id: 'MacBook Pro 14"', quantity: 67, sales_amount: '$2,499.00', hasError: false },
  { sale_date: '09-15-2025', sku_id: 'MacBook Pro 16"', quantity: null, sales_amount: '$3,499.00', hasError: true, errorType: 'invalid' },
  { sale_date: '09-20-2025', sku_id: 'iMac 24"', quantity: 34, sales_amount: '$1,799.00', hasError: false },
  { sale_date: '09-25-2025', sku_id: 'Mac Mini', quantity: 89, sales_amount: '', hasError: true, errorType: 'missing' },
  { sale_date: '10-01-2025', sku_id: 'iPhone 15', quantity: 456, sales_amount: '$899.00', hasError: false },
  { sale_date: '10-05-2025', sku_id: 'iPhone 15 Plus', quantity: 234, sales_amount: '$999.00', hasError: false },
  { sale_date: '10-10-2025', sku_id: 'AirPods', quantity: 789, sales_amount: '$179.00', hasError: false },
  { sale_date: '10-15-2025', sku_id: 'AirPods Max', quantity: 45, sales_amount: '$549.00', hasError: false },
  { sale_date: '10-20-2025', sku_id: 'Apple Pencil', quantity: 123, sales_amount: '$129.00', hasError: false },
  { sale_date: '10-25-2025', sku_id: 'Smart Folio', quantity: 89, sales_amount: '$179.00', hasError: false },
  { sale_date: '11-01-2025', sku_id: 'USB-C Cable', quantity: 567, sales_amount: '$29.00', hasError: false },
  { sale_date: '11-05-2025', sku_id: 'Power Adapter', quantity: null, sales_amount: '$79.00', hasError: true, errorType: 'invalid' },
  { sale_date: '11-10-2025', sku_id: 'iPhone Case', quantity: 890, sales_amount: '$49.00', hasError: false },
  { sale_date: '11-15-2025', sku_id: 'Screen Protector', quantity: 1234, sales_amount: '$39.00', hasError: false },
  { sale_date: '11-20-2025', sku_id: 'Apple TV 4K', quantity: 67, sales_amount: '$179.00', hasError: false },
  { sale_date: '11-25-2025', sku_id: 'HomePod mini', quantity: 234, sales_amount: '', hasError: true, errorType: 'missing' },
  { sale_date: '12-01-2025', sku_id: 'Studio Display', quantity: 23, sales_amount: '$1,599.00', hasError: false },
  { sale_date: '12-05-2025', sku_id: 'Pro Display XDR', quantity: 5, sales_amount: '$4,999.00', hasError: false }
];

export const locationsMockData: LocationRow[] = [
  { location_id: 'LOC001', location_name: 'New York Store', region: 'North America', country: 'USA', hasError: false },
  { location_id: 'LOC002', location_name: 'London Store', region: 'Europe', country: 'UK', hasError: false },
  { location_id: 'LOC003', location_name: 'Tokyo Store', region: 'Asia', country: 'Japan', hasError: false },
  { location_id: 'LOC004', location_name: 'Sydney Store', region: 'Oceania', country: 'Australia', hasError: false },
  { location_id: 'LOC005', location_name: 'Paris Store', region: 'Europe', country: 'France', hasError: false },
  { location_id: 'LOC006', location_name: 'Berlin Store', region: 'Europe', country: 'Germany', hasError: false },
  { location_id: 'LOC007', location_name: 'Toronto Store', region: 'North America', country: 'Canada', hasError: false },
  { location_id: 'LOC008', location_name: 'Mumbai Store', region: 'Asia', country: '', hasError: true, errorType: 'missing' },
  { location_id: 'LOC009', location_name: 'Singapore Store', region: 'Asia', country: 'Singapore', hasError: false },
  { location_id: 'LOC010', location_name: 'Dubai Store', region: 'Middle East', country: 'UAE', hasError: false },
  { location_id: 'LOC011', location_name: 'San Francisco Store', region: 'North America', country: 'USA', hasError: false },
  { location_id: 'LOC012', location_name: 'Los Angeles Store', region: 'North America', country: 'USA', hasError: false },
  { location_id: 'LOC013', location_name: 'Chicago Store', region: '', country: 'USA', hasError: true, errorType: 'missing' },
  { location_id: 'LOC014', location_name: 'Miami Store', region: 'North America', country: 'USA', hasError: false },
  { location_id: 'LOC015', location_name: 'Boston Store', region: 'North America', country: 'USA', hasError: false }
];

export const skusMockData: SkuRow[] = [
  { sku_id: 'SKU001', product_name: 'iPhone 15 Pro', category: 'Electronics', price: '$999.00', hasError: false },
  { sku_id: 'SKU002', product_name: 'Mac Book Air', category: 'Computers', price: '$1299.00', hasError: false },
  { sku_id: 'SKU003', product_name: 'iPad Pro', category: 'Tablets', price: '$1099.00', hasError: false },
  { sku_id: 'SKU004', product_name: 'AirPods Pro', category: 'Audio', price: '$249.00', hasError: false },
  { sku_id: 'SKU005', product_name: 'Apple Watch', category: 'Wearables', price: '$399.00', hasError: false },
  { sku_id: 'SKU006', product_name: 'Mac Studio', category: 'Computers', price: '$1999.00', hasError: false },
  { sku_id: 'SKU007', product_name: 'iPhone 14', category: '', price: '$799.00', hasError: true, errorType: 'missing' },
  { sku_id: 'SKU008', product_name: 'iPad Air', category: 'Tablets', price: '$599.00', hasError: false },
  { sku_id: 'SKU001', product_name: 'HomePod', category: 'Audio', price: '$299.00', hasError: true, errorType: 'duplicate' },
  { sku_id: 'SKU010', product_name: 'Apple TV', category: 'Entertainment', price: '$149.00', hasError: false },
  { sku_id: 'SKU011', product_name: 'Magic Keyboard', category: 'Accessories', price: '$349.00', hasError: false },
  { sku_id: 'SKU012', product_name: 'Magic Mouse', category: 'Accessories', price: '', hasError: true, errorType: 'missing' },
  { sku_id: 'SKU013', product_name: 'MacBook Pro 14"', category: 'Computers', price: '$2499.00', hasError: false },
  { sku_id: 'SKU014', product_name: 'MacBook Pro 16"', category: 'Computers', price: '$3499.00', hasError: false },
  { sku_id: 'SKU015', product_name: 'iMac 24"', category: 'Computers', price: '$1799.00', hasError: false },
  { sku_id: 'SKU016', product_name: 'Mac Mini', category: 'Computers', price: '$699.00', hasError: false },
  { sku_id: 'SKU017', product_name: 'iPhone 15', category: 'Electronics', price: '$899.00', hasError: false },
  { sku_id: 'SKU018', product_name: 'iPhone 15 Plus', category: 'Electronics', price: '$999.00', hasError: false },
  { sku_id: 'SKU019', product_name: 'AirPods', category: 'Audio', price: '$179.00', hasError: false },
  { sku_id: 'SKU020', product_name: 'AirPods Max', category: 'Audio', price: '$549.00', hasError: false }
];

export interface TableData {
  flashana_schema_id?: string;
  uploaded_file_batch_id?: string;
  sales_data: SalesDataRow[];
  locations: LocationRow[];
  skus: SkuRow[];
}

export const getTableData = async (
  flashana_schema_id?: string,
  uploaded_file_batch_id?: string
): Promise<TableData> => {
  await new Promise(resolve => setTimeout(resolve, 300));
  
  return {
    flashana_schema_id,
    uploaded_file_batch_id,
    sales_data: salesMockData,
    locations: locationsMockData,
    skus: skusMockData
  };
};