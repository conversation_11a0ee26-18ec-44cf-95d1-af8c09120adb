import { useState, useRef, useEffect } from "react";
import {
  X,
  Upload,
  CheckCircle,
  AlertCircle,
  Trash2,
  CloudUploadIcon,
} from "lucide-react";
import {
  uploadPreSignedUrl,
  useUploadCorrectedFile,
} from "@/hooks/validateFileUploads/validateFileUploads";
import { useToast } from "@/hooks/useToast";
import { useCheckUploadCorrectedFileStatus } from "@/hooks/fileValidate/uploadCorrectedFileStatus";
import { Card, CardContent } from "../ui/card";
import { Progress } from "../ui/progress";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Info } from "lucide-react";
import { AlertDialog, AlertDialogDescription } from "../ui/alert-dialog";

interface UploadFile {
  id: string;
  file: File;
  status: "pending" | "uploading" | "completed" | "error";
  progress: number;
  error?: string;
}

interface UploadModalProps {
  isOpen: boolean;
  onClose: () => void;
  batchId: number;
  onValidateComplete: any;
}

export default function UploadModal({
  isOpen,
  onClose,
  batchId,
  onValidateComplete,
}: UploadModalProps) {
  const [uploadFiles, setUploadFiles] = useState<UploadFile[]>([]);
  const [isDragging, setIsDragging] = useState(false);
  const [isUploading, setIsUploading] = useState(false);
  const [overallProgress, setOverallProgress] = useState(0);
  const [isValidated, setIsValidated] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [isLoading, setIsLoading] = useState(false);
  const { toasts, removeToast, success, error } = useToast();
  const [uploadStatusApiCall, setUploadStatusApiCall] = useState(false);
  const [pollAttempts, setPollAttempts] = useState(0);
  const [isCheckingAvailability, setIsCheckingAvailability] = useState(false);
  const [showAvailabilityError, setShowAvailabilityError] = useState(false);

  // ✅ Place useEffect right here
  useEffect(() => {
    if (isOpen) {
      setUploadFiles([]);
      setIsValidated(false);
      setOverallProgress(0);
    }
  }, [isOpen]);

  const allowedFileTypes = [".xlsx", ".xls"];
  const maxFileSize = 25 * 1024 * 1024;

  const validateFile = (file: File): string | null => {
    const extension = "." + file.name.split(".").pop()?.toLowerCase();
    if (!allowedFileTypes.includes(extension)) {
      return `File type ${extension} is not supported. Only .xls and .xlsx allowed.`;
    }
    if (file.size > maxFileSize) {
      return `File size exceeds 25MB limit. Current size: ${(file.size / 1024 / 1024).toFixed(1)} MB`;
    }
    return null;
  };

  const handleFileSelect = (files: FileList | null) => {
    if (!files || files.length === 0) return;

    const file = files[0]; // only single file allowed
    const error = validateFile(file);

    setUploadFiles([
      {
        id: Date.now().toString(),
        file,
        status: error ? "error" : "pending",
        progress: 0,
        error,
      },
    ]);

    // ✅ Clear the input so same file can be reselected
    if (fileInputRef.current) {
      fileInputRef.current.value = "";
    }
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(true);
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(false);
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(false);
    handleFileSelect(e.dataTransfer.files);
  };

  const removeFile = (fileId: string) => {
    setUploadFiles((prev) => prev.filter((f) => f.id !== fileId));
    setIsValidated(false);
  };

  const startUpload = async () => {
    const fileObj = uploadFiles[0];
    if (!fileObj || fileObj.status === "error") return;

    setIsUploading(true);
    setOverallProgress(0);

    const payloadData: any = {
      batch_id: batchId,
      file_name: uploadFiles && uploadFiles[0]?.file?.name,
    };
    console.log("File", uploadFiles && uploadFiles[0]?.file?.name);
    uploadCorrectedFile(payloadData);

    // setUploadFiles((prev) =>
    //   prev.map((f) =>
    //     f.id === fileObj.id ? { ...f, status: "uploading", progress: 0 } : f
    //   )
    // );

    // for (let progress = 0; progress <= 100; progress += 10) {
    //   await new Promise((resolve) => setTimeout(resolve, 100));
    //   setUploadFiles((prev) =>
    //     prev.map((f) =>
    //       f.id === fileObj.id ? { ...f, progress: progress } : f
    //     )
    //   );
    //   setOverallProgress(progress);
    // }

    // setUploadFiles((prev) =>
    //   prev.map((f) =>
    //     f.id === fileObj.id ? { ...f, status: "completed", progress: 100 } : f
    //   )
    // );

    // setIsUploading(false);
  };

  const handleValidate = () => {
    setUploadFiles([]);
    setIsUploading(false);
    setOverallProgress(0);
    setIsValidated(true);
    onValidateComplete(true);
    onClose(); // close modal after validate

    setPollAttempts(0);
    setIsCheckingAvailability(false);
    setShowAvailabilityError(false);
  };

  const canUpload =
    uploadFiles.length === 1 &&
    uploadFiles[0].status !== "error" &&
    !isUploading;

  const canValidate =
    uploadFiles.length === 1 && uploadFiles[0].status === "completed";

  if (!isOpen) return null;

  const { mutate: uploadCorrectedFile } = useUploadCorrectedFile(
    async (data) => {
      const uploadUrl = data?.result?.data?.upload_url;
      const file = uploadFiles[0];
      console.log("sucess called,,,,", data);

      if (uploadUrl && file) {
        try {
          const uploadFileAndUrl: any = {
            upload_url: uploadUrl,
            file: file,
          };
          await uploadFileUsingPresignedUrl(uploadFileAndUrl);
          setUploadStatusApiCall(true);
          console.log("sucess called,,,,");
        } catch (uploadError) {
          error("Upload Error", "Failed to upload the file. Please try again.");
        }
      } else {
        error(
          "Upload Error",
          `Missing file or upload URL for corrected file upload.`
        );
      }
    },
    (errorRes: any) => {
      setIsLoading(false);
      const errorInfo =
        (errorRes as any)?.error?.message ||
        "Something went wrong. Please try again.";
      error("Upload Error", errorInfo);
    }
  );

  const { mutate: uploadFileUsingPresignedUrl } = uploadPreSignedUrl(
    (data) => {
      // After successful upload, start checking corrected data availability
      setIsLoading(false);
      setIsCheckingAvailability(true);
      setPollAttempts(0); // Reset counter before starting
      checkCorrectedDataAvailability(String(batchId));
      success("Success", `File uploaded successfully to presigned URL`);
    },
    (errorRes) => {
      setIsLoading(false);
      error("Error", `Failed to uploaded file on presigned URL: ${errorRes}`);
    }
  );

  // Batch availability checking hook
  const { mutate: checkCorrectedDataAvailability } =
    useCheckUploadCorrectedFileStatus(
      (data) => {
        const status = data?.result?.data?.status || data?.data?.status;
        const currentAttempts = pollAttempts + 1;
        setPollAttempts(currentAttempts);

        if (status === true) {
          // Files are available on S3, proceed with success flow
          setIsCheckingAvailability(false);
          setIsLoading(false);
          setPollAttempts(0); // Reset counter
          success(
            "Success",
            `The corrected file has been uploaded and is now available on S3.`
          );
          handleValidate();
        } else if (currentAttempts >= 20) {
          // Maximum attempts reached, show error
          setShowAvailabilityError(true);
          setIsUploading(false);
          setIsCheckingAvailability(false);
          setIsLoading(false);
          setPollAttempts(0); // Reset counter
          error(
            "Error",
            "Something went wrong. Files are taking too long to be available on S3. Please try again."
          );
        } else {
          // Files not yet available, continue polling
          setTimeout(() => {
            if (batchId) {
              checkCorrectedDataAvailability(String(batchId));
            }
          }, 2000); // Poll every 2 seconds
        }
      },
      (errorRes) => {
        setIsCheckingAvailability(false);
        setIsLoading(false);
        setPollAttempts(0); // Reset counter
        error("Error", `Failed to check batch availability: ${errorRes}`);
      }
    );

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle className="text-xl font-semibold">
            Upload Corrected File
          </DialogTitle>
        </DialogHeader>
        <div className="px-4">
          {/* Info Label */}
          <div className="mb-4 p-4 rounded bg-cyan-50 border border-cyan-500 text-cyan-700 text-sm font-medium flex items-center justify-between">
            <span className="flex gap-2">
              <Info size={20} className="min-w-[1rem]" />{" "}
              <span>
                You can upload only one file at a time, and it must be in XLS
                format.
              </span>
            </span>
          </div>

          {/* Drag & Drop Area */}
          <div
            onDragOver={handleDragOver}
            onDragLeave={handleDragLeave}
            onDrop={handleDrop}
            onClick={() => fileInputRef.current?.click()}
            className={`
            border-2 border-dashed border-gray-300 rounded-lg bg-gray-50 p-8 text-center mb-2
            ${isDragging ? "border-emerald-500 bg-emerald-50" : ""}
            ${isUploading ? "cursor-not-allowed" : "cursor-pointer"}
          `}
          >
            <div className="flex flex-col items-center justify-center">
              <CloudUploadIcon size={40} className="text-gray-500 mb-3" />
              <h4 className="text-lg font-semibold text-zinc-900 mb-2">
                <span>
                  <span className="text-amber-700">Click to Upload</span> or
                  Drag & Drop
                </span>
              </h4>
              <p className="text-gray-500 mb-4">Supported formats: CSV, XLSX</p>
            </div>
            <input
              type="file"
              ref={fileInputRef}
              accept=".xlsx,.xls"
              onChange={(e) => handleFileSelect(e.target.files)}
              className="hidden"
              disabled={isUploading}
            />
          </div>

          {/* Upload Progress */}
          {isUploading && !isCheckingAvailability && (
            <Card className="bg-slate-50">
              <CardContent className="p-6">
                <div className="flex justify-between text-sm text-gray-500 mb-2">
                  <span>Uploading Files</span>
                  <span>{Math.round(overallProgress)}%</span>
                </div>
                <Progress value={overallProgress} className="mb-2" />
                <div className="text-xs text-gray-500">Uploading</div>
              </CardContent>
            </Card>
          )}

          {/* Selected File Header */}
          {uploadFiles.length > 0 && (
            <div className="mb-1">
              <h3 className="text-lg font-medium text-zinc-900">
                Selected File
              </h3>
            </div>
          )}

          {/* File Display */}
          {uploadFiles.map((file) => (
            <Card key={file.id} className="mb-3">
              <CardContent className="p-4">
                <div className="flex justify-between items-center">
                  <div className="font-medium">{file.file.name}</div>
                  <div className="flex items-center gap-2">
                    {file.status === "completed" ? (
                      <CheckCircle size={18} className="text-green-600" />
                    ) : file.status === "error" ? (
                      <AlertCircle size={18} className="text-red-600" />
                    ) : file.status === "uploading" ? (
                      <span className="text-sm">{file.progress}%</span>
                    ) : null}
                    {!isUploading && (
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => removeFile(file.id)}
                        className="p-1 h-auto hover:bg-red-50"
                      >
                        <Trash2 size={16} className="text-red-600" />
                      </Button>
                    )}
                  </div>
                </div>

                {file.status === "uploading" && (
                  <div className="mt-2">
                    <Progress value={file.progress} className="h-1" />
                  </div>
                )}

                {file.error && (
                  <div className="mt-2 p-2 bg-red-50 border border-red-200 rounded text-xs text-red-600">
                    {file.error}
                  </div>
                )}
              </CardContent>
            </Card>
          ))}
        </div>
        {/* Actions */}
        <div className="flex justify-end gap-3 p-4 border-t">
          <Button variant="outline" onClick={onClose} disabled={isUploading}>
            Cancel
          </Button>
          <Button
            onClick={startUpload}
            disabled={!canUpload}
            className="bg-primary hover:bg-primary"
          >
            {isUploading ? "Uploading..." : "Upload"}
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
}
