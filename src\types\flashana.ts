// Authentication Types
export interface User {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  tenantId: string;
  role: 'company_admin' | 'company_user';
  cognitoUserId: string;
  createdAt: string;
  updatedAt: string;
}

export interface LoginRequest {
  email: string;
  password: string;
}

export interface SignupRequest {
  email: string;
  password: string;
  firstName: string;
  lastName: string;
  companyName: string;
  companyAddress?: string;
  companyPhone?: string;
}

// Company Types
export interface Company {
  id: string;
  name: string;
  address?: string;
  phone?: string;
  email?: string;
  taxId?: string;
  createdAt: string;
  updatedAt: string;
}

export interface Holiday {
  id: string;
  name: string;
  date: string;
  type: 'national' | 'company' | 'regional';
  description?: string;
}

// Store Types
export interface Store {
  id: string;
  name: string;
  code: string;
  address: string;
  city: string;
  state: string;
  zipCode: string;
  phone?: string;
  email?: string;
  managerId?: string;
  latitude?: number;
  longitude?: number;
  status: 'active' | 'inactive';
}

// Data Ingestion Types
export interface FileBatch {
  id: string;
  name: string;
  status: 'pending' | 'mapping' | 'validating' | 'validated' | 'imported' | 'failed';
  createdAt: string;
  createdBy: string;
  files: UploadedFile[];
  mappingStatus?: MappingStatus;
  validationStatus?: ValidationStatus;
}

export interface UploadedFile {
  id: string;
  batchId: string;
  name: string;
  size: number;
  type: string;
  status: 'uploaded' | 'mapped' | 'validated' | 'imported' | 'failed';
  uploadedAt: string;
  rowCount?: number;
  columnCount?: number;
  columns?: string[];
}

export interface FieldMapping {
  sourceField: string;
  targetField: string;
  targetTable: string;
  transformations?: string[];
}

export interface MappingStatus {
  tables: TableMappingStatus[];
  overallStatus: 'not_mapped' | 'partially_mapped' | 'mapped';
}

export interface TableMappingStatus {
  tableName: string;
  requiredFields: FieldStatus[];
  mappedFieldsCount: number;
  totalFieldsCount: number;
  status: 'not_mapped' | 'partially_mapped' | 'mapped';
}

export interface FieldStatus {
  fieldName: string;
  isMapped: boolean;
  isRequired: boolean;
  dataType: string;
}

export interface ValidationStatus {
  tables: TableValidationStatus[];
  overallStatus: 'not_validated' | 'partially_validated' | 'validated';
}

export interface TableValidationStatus {
  tableName: string;
  totalRows: number;
  validRows: number;
  invalidRows: number;
  errors: ValidationError[];
  status: 'not_validated' | 'partially_validated' | 'validated';
}

export interface ValidationError {
  row: number;
  field: string;
  value: any;
  error: string;
}

// Master Data Types
export interface Product {
  id: string;
  name: string;
  description?: string;
  categoryId: string;
  brandId: string;
  status: 'active' | 'inactive';
}

export interface SKU {
  id: string;
  productId: string;
  code: string;
  name: string;
  unit: string;
  barcode?: string;
  status: 'active' | 'inactive';
}

export interface Supplier {
  id: string;
  name: string;
  code: string;
  address?: string;
  contactPerson?: string;
  phone?: string;
  email?: string;
  status: 'active' | 'inactive';
}

// Forecasting Types
export interface ForecastConfig {
  algorithm: 'prophet' | 'arima' | 'lstm';
  factors: ForecastFactor[];
  horizon: number; // days
  granularity: 'daily' | 'weekly' | 'monthly';
  confidenceLevel: number; // percentage
}

export interface ForecastFactor {
  name: string;
  type: 'holiday' | 'weather' | 'promotion' | 'event';
  enabled: boolean;
  weight?: number;
}

export interface ForecastResult {
  id: string;
  configId: string;
  createdAt: string;
  status: 'running' | 'completed' | 'failed';
  data: ForecastDataPoint[];
  metrics: ForecastMetrics;
}

export interface ForecastDataPoint {
  date: string;
  actual?: number;
  predicted: number;
  lowerBound: number;
  upperBound: number;
  factors?: Record<string, number>;
}

export interface ForecastMetrics {
  mape: number; // Mean Absolute Percentage Error
  rmse: number; // Root Mean Square Error
  mae: number; // Mean Absolute Error
  accuracy: number; // percentage
}

// Dashboard Types
export interface DashboardMetrics {
  totalSales: number;
  totalStores: number;
  totalProducts: number;
  totalSuppliers: number;
  recentImports: number;
  forecastAccuracy: number;
  topProducts: ProductMetric[];
  salesTrend: SalesTrendPoint[];
}

export interface ProductMetric {
  productId: string;
  productName: string;
  sales: number;
  growth: number;
}

export interface SalesTrendPoint {
  date: string;
  sales: number;
  forecast?: number;
}

// Common Types
export interface ApiResponse<T> {
  data: T;
  message?: string;
  status: 'success' | 'error';
}

export interface PaginatedResponse<T> {
  data: T[];
  total: number;
  page: number;
  pageSize: number;
  totalPages: number;
}

export interface FilterOptions {
  search?: string;
  status?: string;
  dateFrom?: string;
  dateTo?: string;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

// Flashana Schema Tables
export interface FlashanaSchema {
  units: Unit[];
  productCategories: ProductCategory[];
  brands: Brand[];
  products: Product[];
  skus: SKU[];
  suppliers: Supplier[];
  supplierProducts: SupplierProduct[];
  locationTypes: LocationType[];
  locations: Location[];
  channels: Channel[];
  customers: Customer[];
  inventory: Inventory[];
  salesData: SalesData[];
  returns: Return[];
}

export interface Unit {
  id: string;
  name: string;
  abbreviation: string;
  type: 'volume' | 'weight' | 'count' | 'length';
}

export interface ProductCategory {
  id: string;
  name: string;
  parentId?: string;
  description?: string;
}

export interface Brand {
  id: string;
  name: string;
  description?: string;
  logo?: string;
}

export interface SupplierProduct {
  id: string;
  supplierId: string;
  productId: string;
  supplierProductCode: string;
  price: number;
  leadTime: number; // in days
}

export interface LocationType {
  id: string;
  name: 'warehouse' | 'store' | 'distribution_center';
  description?: string;
}

export interface Location {
  id: string;
  name: string;
  typeId: string;
  address: string;
  city: string;
  state: string;
  zipCode: string;
  latitude?: number;
  longitude?: number;
}

export interface Channel {
  id: string;
  name: string;
  type: 'online' | 'retail' | 'wholesale' | 'b2b';
  description?: string;
}

export interface Customer {
  id: string;
  name: string;
  email?: string;
  phone?: string;
  type: 'individual' | 'business';
  channelId: string;
}

export interface Inventory {
  id: string;
  skuId: string;
  locationId: string;
  quantity: number;
  reorderPoint: number;
  reorderQuantity: number;
  lastUpdated: string;
}

export interface SalesData {
  id: string;
  date: string;
  skuId: string;
  locationId: string;
  customerId?: string;
  channelId: string;
  quantity: number;
  unitPrice: number;
  totalPrice: number;
  discount?: number;
}

export interface Return {
  id: string;
  salesId: string;
  date: string;
  quantity: number;
  reason: string;
  status: 'pending' | 'approved' | 'rejected' | 'refunded';
}