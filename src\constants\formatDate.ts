import { format, parse, isValid } from "date-fns";
 
/**
 * Formats date strings into MM-dd-yyyy.
 * If invalid, removes time and returns the date part.
 */
export const formatDateValue = (value: any): string => {
  if (typeof value === "string") {
    const trimmed = value.trim();
 
    // Ignore numeric-only strings
    if (/^\d+$/.test(trimmed)) return trimmed;
 
    // Try multiple common date formats
    const formatsToTry = [
      "yyyy-MM-dd'T'HH:mm:ss.SSSX",
      "yyyy-MM-dd'T'HH:mm:ssX",
      "yyyy-MM-dd'T'HH:mm:ss",
      "yyyy-MM-dd HH:mm:ss",
      "yyyy/MM/dd HH:mm:ss",
      "MM/dd/yyyy HH:mm:ss",
      "MM/dd/yyyy",
      "dd/MM/yyyy",
      "d/M/yyyy hh:mm:ss a",
      "yyyy/d/M/ hh:mm:ss a",
      "yyyy-MM-dd",
      "dd-MM-yyyy",
      "d-M-yyyy",
      "yyyy/MM/dd",
      "d/M/yyyy",
    ];
 
    for (const fmt of formatsToTry) {
      const parsed = parse(trimmed, fmt, new Date());
      if (isValid(parsed)) {
        return format(parsed, "MM/dd/yyyy");
      }
    }
 
    // If invalid, but contains both date and time, try extracting the date part
    const parts = trimmed.split(" ");
    const maybeDatePart = parts[0];
    if (maybeDatePart) {
      // Try parsing just the date part
      for (const fmt of ["MM/dd/yyyy", "dd/MM/yyyy", "yyyy-MM-dd", "yyyy/MM/dd", "d/M/yyyy"]) {
        const parsed = parse(maybeDatePart, fmt, new Date());
        if (isValid(parsed)) {
          return format(parsed, "MM/dd/yyyy");
        }
      }
      return maybeDatePart;
    }
  }
 
  // If value is a Date object
  if (value instanceof Date && isValid(value)) {
    return format(value, "MM/dd/yyyy");
  }
 
  return String(value);
};