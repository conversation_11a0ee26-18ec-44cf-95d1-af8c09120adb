import { Outlet, Link, useLocation, useNavigate } from "react-router-dom";
import {
  LayoutDashboard,
  Store,
  Building2,
  Database,
  TrendingUp,
  ChevronRight,
  Menu,
  User,
  LogOut,
  X,
  ChevronLeft,
  PanelLeft,
} from "lucide-react";
import { useEffect, useState } from "react";
import { useAuthStore } from "@/store/authStore";
import { cn } from "@/lib/utils";
import FlashanaLogo from "@/components/common/FlashanaLogo";
import {
  DropdownMenu,
  DropdownMenuTrigger,
  DropdownMenuContent,
  DropdownMenuItem,
} from "@/components/ui/dropdown-menu";
import {
  Sheet,
  SheetContent,
  SheetHeader,
  SheetTitle,
  SheetClose,
} from "@/components/ui/sheet";
import { useQueryClient } from "@tanstack/react-query";

// Map menu titles to icons
const menuIcons: Record<string, React.ReactNode> = {
  Dashboard: <span className="icon-Dashboard"></span>,
  Stores: <span className="icon-Stores"></span>,
  Company: <span className="icon-Company-Information"></span>,
  "Data Ingestion": <span className="icon-Data-Ingestion"></span>,
  Forecasting: <span className="icon-Forecasting"></span>,
};

const SIDEBAR_WIDTH = 320;
const HEADER_HEIGHT = 72;
const FOOTER_HEIGHT = 48;

const navigation = [
  {
    title: "Dashboard",
    name: "Dashboard",
    href: "/dashboard",
    current: true,
  },
  {
    title: "Company",
    name: "Company",
    href: "/company",
    hasSubmenu: true,
    submenu: [
      { name: "Company Details", href: "/company/details" },
      { name: "Holiday List", href: "/company/holidays" },
    ],
  },
  {
    title: "Stores",
    name: "Stores",
    href: "/stores",
  },
  {
    title: "Data Ingestion",
    name: "Data Ingestion",
    href: "/data-ingestion",
    hasSubmenu: true,
    submenu: [
      { name: "File Batches", href: "/data-ingestion/batches" },
      { name: "Imported Data", href: "/data-ingestion/imported" },
    ],
  },
  {
    title: "Forecasting",
    name: "Forecasting",
    href: "/forecasting",
    hasSubmenu: true,
    submenu: [
      { name: "Forecast List", href: "/forecasting/list" },
      { name: "Generate Forecasts", href: "/forecasting/generate" },
    ],
  },
];

export default function MainLayout() {
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);
  const [sidebarOpen, setSidebarOpen] = useState(false); // for mobile
  const [expandedMenus, setExpandedMenus] = useState<string[]>([]);
  const { user } = useAuthStore();
  const location = useLocation();
  const navigate = useNavigate();
  const logout = useAuthStore((state) => state.logout);
  const currentUser = useAuthStore.getState().user;
  const queryClient = useQueryClient();

  const toggleSubmenu = (menuName: string) => {
    setExpandedMenus((prev) =>
      prev.includes(menuName)
        ? prev.filter((name) => name !== menuName)
        : [...prev, menuName]
    );
  };

  const isActiveMenu = (item: any) => {
    return location.pathname.startsWith(item.href);
  };

  const handleLogout = () => {
    // Implement your logout logic here
    console.log("Logout clicked");

    if (queryClient && currentUser?.user_id) {
      // Clear all user-specific queries
      queryClient.removeQueries({
        predicate: (query: any) => {
          return (
            query.queryKey.includes("user") &&
            query.queryKey.includes(currentUser.user_id)
          );
        },
      });

      // Clear all dashboard and forecast related queries
      queryClient.removeQueries({
        predicate: (query: any) => {
          const keyString = JSON.stringify(query.queryKey);
          return (
            keyString.includes("batch-list") ||
            keyString.includes("dashboard-data") ||
            keyString.includes("store-list") ||
            keyString.includes("forecast-list") ||
            keyString.includes("forecast-results") ||
            keyString.includes("forecast-progress") ||
            keyString.includes("sku-list") ||
            keyString.includes("forecast-algorithms") ||
            keyString.includes("forecast-factors")
          );
        },
      });

      // Clear all queries to be extra safe
      queryClient.clear();

      console.log(`🧹 Cleared all cache for user: ${currentUser.user_id}`);
    }

    // Call logout after clearing cache
    logout();
  };

  const [isMobile, setIsMobile] = useState(false);

  useEffect(() => {
    const checkIsMobile = () => {
      setIsMobile(window.innerWidth < 1024);
    };

    checkIsMobile(); // run once on mount
    window.addEventListener("resize", checkIsMobile);
    return () => window.removeEventListener("resize", checkIsMobile);
  }, []);

  return (
    <div className="flex min-h-screen">
      {/* Sidebar for desktop */}
      <aside
        className={`
          fixed top-0 left-0 z-30 h-screen bg-white border-r flex-shrink-0 transition-all duration-200
          ${sidebarCollapsed ? "w-0 min-w-0 overflow-hidden" : "w-[320px]"}
          hidden lg:flex
        `}
      >
        <div className="flex w-full h-full flex-col">
          {/* Logo */}
          <div className="flex h-16 items-center justify-center px-6 py-4">
            <FlashanaLogo
              variant="full"
              size="medium"
              animated={false}
              onClick={() => navigate("/dashboard")}
            />
          </div>
          {/* Navigation */}
          <nav className="flex-1 px-4 py-3 flex flex-col gap-2">
            {navigation.map((item) => {
              const isActive = isActiveMenu(item);
              const isExpanded = expandedMenus.includes(item.name);

              return (
                <div key={item.name}>
                  {item.hasSubmenu ? (
                    <button
                      onClick={() => toggleSubmenu(item.name)}
                      className={cn(
                        "flashana-nav-item w-full text-left",
                        isActive && "active"
                      )}
                    >
                      <span className="flex items-center gap-3 flex-1">
                        {menuIcons[item.title]}
                        {item.title}
                      </span>
                      <ChevronRight
                        className={cn(
                          "h-4 w-4 transition-transform",
                          isExpanded && "rotate-90"
                        )}
                      />
                    </button>
                  ) : (
                    <Link
                      to={item.href}
                      className={cn(
                        "flashana-nav-item flex items-center text-zinc-600 gap-4",
                        isActive && "active"
                      )}
                    >
                      {menuIcons[item.title]}
                      {item.name}
                    </Link>
                  )}

                  {/* Submenu */}
                  {item.hasSubmenu && isExpanded && (
                    <div className="ml-[24px]">
                      {item.submenu?.map((subItem) => (
                        <Link
                          key={subItem.name}
                          to={subItem.href}
                          className={cn(
                            "submenu-dash block px-[12px] py-[6px] text-zinc-600 border-l mt-0 transition-colors text-sm",
                            location.pathname === subItem.href &&
                            "font-semibold text-primary block"
                          )}
                        >
                          <span
                            className={cn(
                              location.pathname === subItem.href &&
                              "font-semibold text-primary bg-primary/10 rounded-md px-4 py-2.5"
                            )}
                          >
                            {subItem.name}
                          </span>
                        </Link>
                      ))}
                    </div>
                  )}
                </div>
              );
            })}
          </nav>
        </div>
      </aside>

      {/* Sidebar as Drawer for mobile/tablet */}
      <Sheet open={sidebarOpen} onOpenChange={setSidebarOpen}>
        <SheetContent side="left" className="p-0 w-[260px] lg:hidden">
          <SheetHeader className="flex flex-row items-center justify-between px-6 py-4 border-b">
            <SheetTitle>
              <FlashanaLogo
                variant="full"
                size="medium"
                animated={false}
                onClick={() => {
                  setSidebarOpen(false);
                  navigate("/dashboard");
                }}
              />
            </SheetTitle>
            <SheetClose asChild>
              <button className="rounded-full hover:bg-gray-100 p-2">
                {/* <X className="w-6 h-6" /> */}
              </button>
            </SheetClose>
          </SheetHeader>
          <nav className="px-4 py-3 flex flex-col gap-2">
            {navigation.map((item) => {
              const isActive = isActiveMenu(item);
              const isExpanded = expandedMenus.includes(item.name);

              return (
                <div key={item.name}>
                  {item.hasSubmenu ? (
                    <button
                      onClick={() => toggleSubmenu(item.name)}
                      className={cn(
                        "flashana-nav-item w-full text-left",
                        isActive && "active"
                      )}
                    >
                      <span className="flex items-center gap-3 flex-1">
                        {menuIcons[item.title]}
                        {item.title}
                      </span>
                      <ChevronRight
                        className={cn(
                          "h-4 w-4 transition-transform",
                          isExpanded && "rotate-90"
                        )}
                      />
                    </button>
                  ) : (
                    <Link
                      to={item.href}
                      className={cn(
                        "flashana-nav-item flex items-center text-zinc-600 gap-3",
                        isActive && "active"
                      )}
                      onClick={() => setSidebarOpen(false)}
                    >
                      {menuIcons[item.title]}
                      {item.name}
                    </Link>
                  )}

                  {/* Submenu */}
                  {item.hasSubmenu && isExpanded && (
                    <div className="ml-[24px]">
                      {item.submenu?.map((subItem) => (
                        <Link
                          key={subItem.name}
                          to={subItem.href}
                          className={cn(
                            "submenu-dash block px-[18px] py-[14px] text-zinc-600 border-l mt-0 transition-colors text-sm",
                            location.pathname === subItem.href &&
                            "font-semibold text-primary"
                          )}
                          onClick={() => setSidebarOpen(false)}
                        >
                          {subItem.name}
                        </Link>
                      ))}
                    </div>
                  )}
                </div>
              );
            })}
          </nav>
        </SheetContent>
      </Sheet>

      {/* Main area: header, content, footer */}
      <div className="flex flex-col flex-1 min-h-screen transition-all duration-200">
        {/* Header */}
        <header
          className="fixed top-0 right-0 z-20 bg-white border-b flex items-center px-6"
          style={{
            left: isMobile ? 0 : sidebarCollapsed ? 0 : SIDEBAR_WIDTH,
            height: HEADER_HEIGHT,
            minHeight: HEADER_HEIGHT,
            width: `calc(100% - ${isMobile ? 0 : sidebarCollapsed ? 0 : SIDEBAR_WIDTH}px)`,
          }}
        >
          <div className="flex items-center justify-between w-full">
            <div className="flex items-center">
              {/* Single toggle icon for all screens */}
              <button
                type="button"
                className="mr-2"
                onClick={() => {
                  if (window.innerWidth < 1024) {
                    setSidebarOpen((open) => !open);
                  } else {
                    setSidebarCollapsed((collapsed) => !collapsed);
                  }
                }}
                aria-label={
                  sidebarCollapsed || sidebarOpen
                    ? "Open sidebar"
                    : "Collapse sidebar"
                }
              >
                {/* Icon logic */}
                {window.innerWidth < 1024 ? (
                  <PanelLeft className="h-5 w-5" />
                ) : sidebarCollapsed ? (
                  <PanelLeft className="h-5 w-5" />
                ) : (
                  <PanelLeft className="h-5 w-5" />
                )}
              </button>
              {/* Separator */}
              <span className="h-6 w-px bg-gray-300 mx-2" />
              <h1 className="text-xl font-semibold text-gray-900 ml-2">
                {navigation.find((item) =>
                  location.pathname.startsWith(item.href)
                )?.name || "Dashboard"}
              </h1>
            </div>
            {/* User menu */}
            <div className="flex items-center space-x-4">
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <button className="flex items-center space-x-2 rounded-lg hover:bg-gray-100 p-2 -m-2 transition-colors focus:outline-none">
                    <div className="w-8 h-8 bg-primary rounded-full flex items-center justify-center">
                      <User className="h-4 w-4 text-white" />
                    </div>
                    <div className="hidden sm:block text-left">
                      <div className="flex items-center space-x-1">
                        <span className="text-sm font-medium text-gray-900">
                          {user?.full_name || "John Asmith"}
                        </span>
                      </div>
                      <div className="text-xs text-gray-500">
                        {user?.role || "Admin"}
                      </div>
                    </div>
                  </button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="start" className="w-40">
                  <DropdownMenuItem onClick={handleLogout}>
                    <LogOut className="w-4 h-4 mr-2" />
                    Logout
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </div>
        </header>

        {/* Main content */}
        <main
          className="bg-[#f7f7f7] p-6 flex-1"
          style={{
            marginLeft: isMobile ? 0 : sidebarCollapsed ? 0 : SIDEBAR_WIDTH,
            marginTop: HEADER_HEIGHT,
            marginBottom: FOOTER_HEIGHT,
            height: `calc(100vh - ${HEADER_HEIGHT * 2}px)`, // header + footer
          }}
        >
          <Outlet />
        </main>

        {/* Footer */}
        <footer
          className="fixed bottom-0 right-0 text-center py-4 border-t bg-white w-full"
          style={{
            left: isMobile ? 0 : sidebarCollapsed ? 0 : SIDEBAR_WIDTH,
            height: FOOTER_HEIGHT,
            minHeight: FOOTER_HEIGHT,
            width: `calc(100% - ${isMobile ? 0 : sidebarCollapsed ? 0 : SIDEBAR_WIDTH}px)`,
          }}
        >
          <p className="text-xs text-gray-500">
            © Copyright 2025, Flashana. All Rights Reserved.
          </p>
        </footer>
      </div>
    </div>
  );
}
