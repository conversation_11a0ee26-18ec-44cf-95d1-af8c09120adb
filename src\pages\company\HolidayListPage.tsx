import { <PERSON>, CardContent, <PERSON>Header, CardTitle } from "@/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Eye, Pencil, Trash2 } from "lucide-react";
import CreateStoreModal from "@/components/stores/CreateStoreModal";
import React, { useEffect, useState } from "react";
import { useToast } from "@/hooks/useToast";
import { ToastContainer } from "@/components/common/Toast";
import { useGetHolidays } from "@/hooks/holiday/holidayListHooks";
import GlobalLoader from "@/components/common/GlobalLoader";

interface Holiday {
  name: string;
  day: string;
  date: string;
}

const HolidayListPage = () => {
  const [selectedYear, setSelectedYear] = useState("2025");
  const [holidays, setHolidays] = useState<Holiday[]>([]);
  const { toasts, removeToast, success, error } = useToast();

  // API call to get holiday list
  const {
    mutate: fetchHolidays,
    data,
    isPending,
  } = useGetHolidays(
    (data) => {
      setHolidays(data?.result?.data?.holidays);
      console.log("Success", holidays);
    },
    (err) => {
      console.error("Error", err);
      const errorMsg =
        err?.error?.message || "Something went wrong. Please try again.";
      error("Error", errorMsg);
    }
  );

  useEffect(() => {
    fetchHolidays({ country: "ca", year: Number(selectedYear) });
  }, [selectedYear]);

  return (
    <div>
      <Card className="rounded-xl shadow-sm">
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="text-lg">Holiday List</CardTitle>
            <Select value={selectedYear} onValueChange={setSelectedYear}>
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="Select year" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="2023">2023</SelectItem>
                <SelectItem value="2024">2024</SelectItem>
                <SelectItem value="2025">2025</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardHeader>
        <CardContent>
          <div className="rounded-lg border bg-background">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className="font-semibold" style={{ width: "40%" }}>
                    Holiday Name
                  </TableHead>
                  <TableHead className="font-semibold" style={{ width: "30%" }}>
                    Day
                  </TableHead>
                  <TableHead className="font-semibold" style={{ width: "30%" }}>
                    Date
                  </TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {isPending || !data ? (
                  <TableRow>
                    <TableCell
                      colSpan={3}
                      className="text-center py-8 text-gray-500"
                    >
                      <GlobalLoader />
                    </TableCell>
                  </TableRow>
                ) : holidays.length > 0 ? (
                  holidays.map((holiday, index) => {
                    const date = new Date(holiday.date);
                    const mm = String(date.getMonth() + 1).padStart(2, "0");
                    const dd = String(date.getDate()).padStart(2, "0");
                    const yyyy = date.getFullYear();
                    const formattedDate = `${mm}-${dd}-${yyyy}`;

                    return (
                      <TableRow key={index}>
                        <TableCell className="py-3 px-2">
                          {holiday.name}
                        </TableCell>
                        <TableCell className="py-3">{holiday.day}</TableCell>
                        <TableCell className="py-3">{formattedDate}</TableCell>
                      </TableRow>
                    );
                  })
                ) : (
                  <TableRow>
                    <TableCell
                      colSpan={3}
                      className="text-center py-8 text-gray-500"
                    >
                      No Data Found
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>
      <ToastContainer toasts={toasts} onClose={removeToast} />
    </div>
  );
};

export default HolidayListPage;
