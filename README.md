# Flashana Frontend

A modern React application for supply chain data standardization and demand forecasting.

## Quick Start

```bash
# Install dependencies
npm install

# Start development server
npm run dev

# Build for production
npm run build

# Preview production build
npm run preview
```

## Project Structure

```
flashana-frontend/
├── public/                     # Static assets
│   ├── flashana-logo.png      # Application logo
│   └── login-building.png     # Login page background
├── src/
│   ├── assets/                # Application assets
│   ├── components/            # Reusable UI components
│   │   ├── common/           # Shared components (Logo, Pagination, Toast)
│   │   ├── data-ingestion/   # Data upload and processing components
│   │   ├── forecasting/      # Chart and forecast visualization
│   │   ├── layout/           # Main layout wrapper
│   │   ├── settings/         # Settings modals and forms
│   │   ├── stores/           # Store management components
│   │   └── ui/               # Base UI components (Button, Card, Input)
│   ├── contexts/             # React context providers
│   │   └── ThemeContext.tsx  # Theme management
│   ├── data/                 # Static data and mock data
│   ├── hooks/                # Custom React hooks
│   │   ├── useFileUpload.ts  # File upload functionality
│   │   ├── usePagination.ts  # Pagination logic
│   │   ├── useSession.ts     # Session management
│   │   ├── useToast.ts       # Toast notifications
│   │   └── useValidation.ts  # Data validation
│   ├── lib/                  # Utility libraries
│   │   └── utils.ts          # Common utility functions
│   ├── mockData/             # JSON mock data for development
│   ├── pages/                # Page components
│   │   ├── auth/             # Login and signup pages
│   │   ├── company/          # Company details management
│   │   ├── dashboard/        # Main dashboard
│   │   ├── data-ingestion/   # Data import and validation pages
│   │   ├── forecasting/      # Demand forecasting interface
│   │   ├── profile/          # User profile management
│   │   ├── settings/         # Application settings
│   │   └── stores/           # Store management
│   ├── services/             # API and external services
│   │   ├── api.ts            # HTTP client and API endpoints
│   │   └── validationService.ts # Data validation logic
│   ├── store/                # State management (Zustand)
│   │   ├── authStore.ts      # Authentication state
│   │   └── useSessionStore.ts # Session state
│   ├── types/                # TypeScript type definitions
│   │   ├── flashana.ts       # Domain-specific types
│   │   └── index.ts          # Shared type definitions
│   ├── utils/                # Utility functions
│   │   ├── formatters.ts     # Data formatting utilities
│   │   ├── transformers.ts   # Data transformation logic
│   │   └── validators.ts     # Input validation functions
│   ├── App.tsx               # Main application component
│   ├── main.tsx              # Application entry point
│   └── router.tsx            # Application routing configuration
├── index.html                # HTML template
├── package.json              # Dependencies and scripts
├── tailwind.config.js        # Tailwind CSS configuration
├── tsconfig.json            # TypeScript configuration
└── vite.config.ts           # Vite build configuration
```

## Key Features

- **Authentication**: Login/signup with session management
- **Data Ingestion**: Excel/CSV file upload with field mapping and validation
- **Dashboard**: Overview of key metrics and recent activities
- **Forecasting**: Interactive demand forecasting with chart visualization
- **Store Management**: Create and manage retail locations
- **Company Settings**: Configure company details and holidays
- **Profile Management**: User account settings

## Technology Stack

- **Frontend Framework**: React 18 with TypeScript
- **Build Tool**: Vite
- **Styling**: Tailwind CSS
- **State Management**: Zustand
- **Data Fetching**: TanStack Query (React Query)
- **Charts**: Chart.js with react-chartjs-2
- **Forms**: React Hook Form with Yup validation
- **File Processing**: xlsx library for Excel files
- **Routing**: React Router v6
- **UI Components**: Custom components with Radix UI primitives

## Development

- **Linting**: ESLint with TypeScript rules
- **Formatting**: Prettier
- **Type Checking**: TypeScript strict mode
- **Hot Reload**: Vite HMR for fast development

## Architecture

The application follows a modular architecture with:

- **Component-based UI**: Reusable components with clear separation of concerns
- **Custom Hooks**: Business logic extracted into reusable hooks
- **Service Layer**: API calls and external integrations abstracted
- **Type Safety**: Full TypeScript coverage for reliable development
- **State Management**: Centralized state with Zustand stores
- **Mock Data**: JSON-based mock data for development and testing

## File Upload & Processing

The application supports:
- Excel (.xlsx, .xls) and CSV file uploads
- Dynamic field mapping interface
- Data validation with error reporting
- Batch processing with progress tracking
- Preview and confirmation workflows