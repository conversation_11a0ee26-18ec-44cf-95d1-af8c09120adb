import React, { createContext, useContext, useState } from 'react';
import { ChevronDown } from 'lucide-react';
import { cn } from '@/lib/utils';

interface AccordionContextType {
  value: string | string[] | undefined;
  onValueChange: (value: string) => void;
  type: 'single' | 'multiple';
}

const AccordionContext = createContext<AccordionContextType | undefined>(undefined);

interface AccordionProps {
  type: 'single' | 'multiple';
  collapsible?: boolean;
  defaultValue?: string | string[];
  className?: string;
  children: React.ReactNode;
}

export function Accordion({ type, collapsible = false, defaultValue, className, children }: AccordionProps) {
  const [value, setValue] = useState<string | string[] | undefined>(
    type === 'multiple' ? (Array.isArray(defaultValue) ? defaultValue : defaultValue ? [defaultValue] : []) : (typeof defaultValue === 'string' ? defaultValue : undefined)
  );

  const onValueChange = (newValue: string) => {
    if (type === 'multiple') {
      setValue((prev) => {
        const arr = Array.isArray(prev) ? prev : [];
        if (arr.includes(newValue)) {
          return collapsible ? arr.filter((v) => v !== newValue) : arr;
        } else {
          return [...arr, newValue];
        }
      });
    } else {
      if (collapsible && value === newValue) {
        setValue(undefined);
      } else {
        setValue(newValue);
      }
    }
  };

  return (
    <AccordionContext.Provider value={{ value, onValueChange, type }}>
      <div className={cn('space-y-2', className)}>
        {children}
      </div>
    </AccordionContext.Provider>
  );
}

interface AccordionItemProps {
  value: string;
  className?: string;
  children: React.ReactNode;
}

export function AccordionItem({ value, className, children }: AccordionItemProps) {
  const context = useContext(AccordionContext);
  if (!context) throw new Error('AccordionItem must be used within Accordion');

  let isOpen = false;
  if (context.type === 'multiple') {
    isOpen = Array.isArray(context.value) && context.value.includes(value);
  } else {
    isOpen = context.value === value;
  }

  return (
    <div className={cn('', className)}>
      {React.Children.map(children, (child) => {
        if (React.isValidElement(child)) {
          return React.cloneElement(child as React.ReactElement<any>, { isOpen, value });
        }
        return child;
      })}
    </div>
  );
}

interface AccordionTriggerProps {
  className?: string;
  children: React.ReactNode;
  isOpen?: boolean;
  value?: string;
}

export function AccordionTrigger({ className, children, isOpen, value }: AccordionTriggerProps) {
  const context = useContext(AccordionContext);
  if (!context) throw new Error('AccordionTrigger must be used within Accordion');

  return (
    <button
      className={cn(
        'flex w-full items-center justify-between text-sm font-medium transition-all p-4',
        className
      )}
      onClick={() => value && context.onValueChange(value)}
    >
      {children}
      <ChevronDown
        className={cn(
          'h-4 w-4 shrink-0 transition-transform duration-200',
          isOpen && 'rotate-180'
        )}
      />
    </button>
  );
}

interface AccordionContentProps {
  className?: string;
  children: React.ReactNode;
  isOpen?: boolean;
}

export function AccordionContent({ className, children, isOpen }: AccordionContentProps) {
  if (!isOpen) return null;

  return (
    <div className={cn('border-t', className)}>
      {children}
    </div>
  );
}