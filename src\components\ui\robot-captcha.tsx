import React, { useState, useEffect } from "react";
import { <PERSON>, <PERSON>Check, Loader2 } from "lucide-react";
import { cn } from "@/lib/utils";

interface RobotCaptchaProps {
  onChange?: (verified: boolean) => void;
  className?: string;
}

export function RobotCaptcha({ onChange, className }: RobotCaptchaProps) {
  const [isChecked, setIsChecked] = useState(false);
  const [isVerifying, setIsVerifying] = useState(false);
  const [isVerified, setIsVerified] = useState(false);
  const [isHovered, setIsHovered] = useState(false);

  const handleClick = async () => {
    if (isVerified) return;

    setIsChecked(true);
    setIsVerifying(true);

    // Simulate verification process
    await new Promise((resolve) => setTimeout(resolve, 1500));

    setIsVerifying(false);
    setIsVerified(true);
    onChange?.(true);
  };

  useEffect(() => {
    if (!isChecked) {
      setIsVerified(false);
      onChange?.(false);
    }
  }, [isChecked, onChange]);

  return (
    <div
      className={cn(
        "relative group cursor-pointer select-none transition-all duration-300",
        "bg-gradient-to-br from-white to-zinc-50",
        "border border-zinc-200 rounded-xl",
        "hover:border-zinc-300 hover:shadow-lg",
        isVerified &&
          "border-green-400 bg-gradient-to-br from-green-50 to-emerald-50",
        className
      )}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      onClick={handleClick}
      style={{
        padding: "1rem",
        overflow: "hidden",
      }}
    >
      {/* Background pattern */}
      <div
        className="absolute inset-0 opacity-5"
        style={{
          backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23000000' fill-opacity='0.1'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
        }}
      />

      <div className="relative flex items-center gap-3">
        {/* Checkbox */}
        <div
          className={cn(
            "relative w-6 h-6 rounded-md transition-all duration-300",
            "border-2 bg-white",
            isHovered && !isVerified && "border-zinc-400 scale-110",
            isVerified && "border-green-500 bg-green-500"
          )}
        >
          {isVerified && (
            <svg
              className="absolute inset-0 w-full h-full p-0.5 text-white animate-in zoom-in-50 duration-300"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
              strokeWidth={3}
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                d="M5 13l4 4L19 7"
              />
            </svg>
          )}
          {isVerifying && (
            <Loader2 className="absolute inset-0 w-full h-full p-0.5 text-zinc-600 animate-spin" />
          )}
        </div>

        {/* Text */}
        <div className="flex flex-col">
          <span
            className={cn(
              "text-sm font-medium transition-colors duration-300",
              isVerified ? "text-green-700" : "text-zinc-700"
            )}
          >
            {isVerifying
              ? "Verifying..."
              : isVerified
                ? "Verified! You're human"
                : "I am not a robot"}
          </span>
          {isHovered && !isVerified && !isVerifying && (
            <span className="text-xs text-zinc-500 animate-in slide-in-from-left-2 duration-300">
              Click to verify
            </span>
          )}
        </div>

        {/* Icon */}
        <div className="ml-auto">
          <div
            className={cn(
              "relative w-12 h-12 rounded-lg transition-all duration-500",
              "bg-gradient-to-br shadow-sm",
              isVerified
                ? "from-green-400 to-emerald-500 rotate-0 scale-100"
                : isHovered
                  ? "from-blue-400 to-indigo-500 rotate-3 scale-110"
                  : "from-zinc-300 to-zinc-400 rotate-0 scale-100"
            )}
          >
            {isVerified ? (
              <ShieldCheck className="absolute inset-0 w-full h-full p-2.5 text-white" />
            ) : (
              <Shield className="absolute inset-0 w-full h-full p-2.5 text-white" />
            )}
          </div>
        </div>
      </div>

      {/* Success animation overlay */}
      {isVerified && (
        <div
          className="absolute inset-0 pointer-events-none"
          style={{
            background:
              "radial-gradient(circle at center, rgba(34, 197, 94, 0.1) 0%, transparent 70%)",
            animation: "pulse-overlay 2s ease-out",
          }}
        />
      )}

      <style>{`
        @keyframes pulse-overlay {
          0% {
            transform: scale(0.5);
            opacity: 0;
          }
          50% {
            opacity: 0.3;
          }
          100% {
            transform: scale(2);
            opacity: 0;
          }
        }
      `}</style>
    </div>
  );
}
