{"companyDetails": {"status": "success", "data": {"id": "tenant_001", "name": "ABC Supply Chain Co.", "address": "123 Business St, New York, NY 10001", "phone": "******-123-4567", "email": "<EMAIL>", "taxId": "12-3456789", "website": "https://www.abcsupplychain.com", "industry": "Retail & Distribution", "employeeCount": 250, "foundedYear": 2015, "description": "Leading supply chain management company specializing in retail distribution", "logo": "https://api.flashana.com/logos/abc-supply-chain.png", "createdAt": "2025-01-01T00:00:00Z", "updatedAt": "2025-01-15T10:30:00Z"}}, "updateCompanySuccess": {"status": "success", "data": {"id": "tenant_001", "name": "ABC Supply Chain Co.", "address": "456 Commerce Ave, New York, NY 10002", "phone": "******-123-4567", "email": "<EMAIL>", "taxId": "12-3456789", "website": "https://www.abcsupplychain.com", "industry": "Retail & Distribution", "employeeCount": 275, "foundedYear": 2015, "description": "Leading supply chain management company specializing in retail distribution and logistics", "logo": "https://api.flashana.com/logos/abc-supply-chain.png", "createdAt": "2025-01-01T00:00:00Z", "updatedAt": "2025-01-20T11:45:00Z"}, "message": "Company details updated successfully"}, "holidays": {"status": "success", "data": {"holidays": [{"id": "holiday_001", "name": "New Year's Day", "date": "2025-01-01", "type": "national", "description": "First day of the year"}, {"id": "holiday_002", "name": "<PERSON> Jr. Day", "date": "2025-01-20", "type": "national", "description": "Federal holiday"}, {"id": "holiday_003", "name": "Company Foundation Day", "date": "2025-03-15", "type": "company", "description": "Anniversary of company founding"}, {"id": "holiday_004", "name": "Independence Day", "date": "2025-07-04", "type": "national", "description": "US Independence Day"}, {"id": "holiday_005", "name": "Thanksgiving", "date": "2025-11-27", "type": "national", "description": "Thanksgiving Day"}, {"id": "holiday_006", "name": "Christmas Day", "date": "2025-12-25", "type": "national", "description": "Christmas holiday"}], "total": 6}}, "addHolidaySuccess": {"status": "success", "data": {"id": "holiday_007", "name": "Summer Company Picnic", "date": "2025-07-15", "type": "company", "description": "Annual company picnic day"}, "message": "Holiday added successfully"}, "users": {"status": "success", "data": {"users": [{"id": "user_001", "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON>", "role": "company_admin", "status": "active", "lastLogin": "2025-01-20T09:00:00Z", "createdAt": "2025-01-01T00:00:00Z"}, {"id": "user_002", "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON>", "role": "company_user", "status": "active", "lastLogin": "2025-01-19T14:30:00Z", "createdAt": "2025-01-05T10:00:00Z"}, {"id": "user_003", "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON>", "role": "company_user", "status": "active", "lastLogin": "2025-01-18T11:00:00Z", "createdAt": "2025-01-10T08:00:00Z"}, {"id": "user_004", "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON>", "role": "company_user", "status": "inactive", "lastLogin": "2025-01-01T12:00:00Z", "createdAt": "2025-01-01T00:00:00Z"}], "total": 4, "page": 1, "pageSize": 10, "totalPages": 1}}, "createUserSuccess": {"status": "success", "data": {"id": "user_005", "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON>", "role": "company_user", "status": "active", "createdAt": "2025-01-20T12:00:00Z"}, "message": "User created successfully. An invitation email has been sent."}}