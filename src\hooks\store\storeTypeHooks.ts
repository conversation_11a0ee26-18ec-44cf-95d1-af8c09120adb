import { useQuery } from "@tanstack/react-query";
import useDataService from "@/services/useDataService";
import { GET_STORE_TYPES_URL } from "@/constants/urls";

export interface StoreType {
  location_type_id: number;
  created_at: string;
  external_id: string | null;
  description: string;
  type_name: string;
  uploaded_file_id: string | null;
}

interface GetStoreTypesResponse {
  success: boolean;
  code: number;
  message: string;
  result: {
    data: StoreType[];
  };
}

export const useGetStoreTypes = () => {
  return useQuery<GetStoreTypesResponse>({
    queryKey: ["store-types"],
    queryFn: async () => {
      return await useDataService.getService(GET_STORE_TYPES_URL);
    },
    staleTime: 0,
  });
};
