/* eslint-disable @typescript-eslint/no-explicit-any */

import { useMutation, useQueryClient } from "@tanstack/react-query";
import useDataService from "../../services/useDataService";
import { OTP_VERIFY_URL, RESEND_OTP_URL } from "../../constants/urls";

export const usePostVerifyOtp = (onPostSuccess: any, onPostError: any) => {
  const queryClient = useQueryClient();
  const {
    mutate: postMutate,
  } = useMutation<any, Error>({
    mutationFn: async (data: any) => {
      const result = await useDataService.postService(`${OTP_VERIFY_URL}`, data);
      return result;
    },
    onSuccess: (data: any) => {
      onPostSuccess(data);
    },
    onSettled: () => {
      queryClient.invalidateQueries({ queryKey: ["otpVerify"] });// You can keep this as a string or key array
    },
    onError: (error: any) => {
        onPostError(error);
    },
  });

  return {postMutate };
};

export const usePostResendOtp = (onPostSuccess: any, onPostError: any) => {
  const queryClient = useQueryClient();
  const {
    mutate: postMutate,
  } = useMutation<any, Error>({
    mutationFn: async (data: any) => {
      const result = await useDataService.postService(`${RESEND_OTP_URL}`, data);
      return result;
    },
    onSuccess: (data: any) => {
      onPostSuccess(data);
    },
    onSettled: () => {
      queryClient.invalidateQueries({ queryKey: ["resendOtp"] });// You can keep this as a string or key array
    },
    onError: (error: any) => {
        onPostError(error);
    },
  });

  return {postMutate };
};
