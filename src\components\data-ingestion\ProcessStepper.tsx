import React from "react";
import { Check } from "lucide-react";

interface ProcessProgressBarProps {
  currentStep: 1 | 2 | 3;
}

const ProcessStepper: React.FC<ProcessProgressBarProps> = ({ currentStep }) => {
  const steps = [
    { number: 1, label: "Upload Supply Chain Data" },
    { number: 2, label: "Field Mapping" },
    { number: 3, label: "Validation" },
  ];

  return (
    <div className="w-full max-w-4xl mx-auto">
      <div className="flex items-center justify-between mb-6">
        {steps.map((step, index) => (
          <React.Fragment key={step.number}>
            <div className="flex items-center">
              <div
                className={`
                  relative flex items-center justify-center w-6 h-6 rounded-full
                  ${
                    currentStep >= step.number
                      ? "bg-primary text-white"
                      : "bg-gray-300 text-gray-600"
                  }
                `}
              >
                {currentStep > step.number ? (
                  <Check className="w-4 h-4" />
                ) : (
                  <span className="text-sm">{step.number}</span>
                )}
              </div>
              <span
                className={`
                  ml-3 text-sm font-medium
                  ${currentStep >= step.number ? "text-gray-900" : "text-gray-500"}
                `}
              >
                {step.label}
              </span>
            </div>

            {index < steps.length - 1 && (
              <div className="flex-1 mx-4">
                <div className="h-1 bg-gray-300 rounded">
                  <div
                    className="h-full bg-primary rounded transition-all duration-300"
                    style={{
                      width:
                        currentStep > step.number
                          ? "100%"
                          : currentStep === step.number
                            ? "50%"
                            : "0%",
                    }}
                  />
                </div>
              </div>
            )}
          </React.Fragment>
        ))}
      </div>
    </div>
  );
};

export default ProcessStepper;
