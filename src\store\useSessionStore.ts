import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';
import { Session } from '../types';

interface SessionState {
  currentSession: Session | null;
  sessions: Session[];
  setCurrentSession: (session: Session | null) => void;
  addSession: (session: Session) => void;
  updateSession: (sessionId: string, updates: Partial<Session>) => void;
  clearSessions: () => void;
}

export const useSessionStore = create<SessionState>()(
  persist(
    (set) => ({
      currentSession: null,
      sessions: [],
      setCurrentSession: (session) => set({ currentSession: session }),
      addSession: (session) =>
        set((state) => ({
          sessions: [...state.sessions, session],
          currentSession: session,
        })),
      updateSession: (sessionId, updates) =>
        set((state) => ({
          sessions: state.sessions.map((s) =>
            s.session_id === sessionId ? { ...s, ...updates } : s
          ),
          currentSession:
            state.currentSession?.session_id === sessionId
              ? { ...state.currentSession, ...updates }
              : state.currentSession,
        })),
      clearSessions: () => set({ sessions: [], currentSession: null }),
    }),
    {
      name: 'compliance-sessions',
      storage: createJSONStorage(() => localStorage),
      partialize: (state) => ({
        sessions: state.sessions,
        currentSession: state.currentSession,
      }),
    }
  )
);