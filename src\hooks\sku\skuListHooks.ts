import { useQuery } from "@tanstack/react-query";
import useDataService from "@/services/useDataService";
import { GET_SKU_LIST_URL } from "@/constants/urls";

export interface SKU {
  sku_id: number;
  sku_code: string;
}

interface GetSKUListResponse {
  success: boolean;
  message: string;
  code: number;
  result: {
    data: SKU[];
  };
}

export const useGetSKUList = (storeId?: string) => {

  return useQuery<GetSKUListResponse>({
    queryKey: ["sku-list", storeId],
    queryFn: async () => {
      if (!storeId) throw new Error("storeId is required to fetch SKUs");
      return await useDataService.getService(
        `${GET_SKU_LIST_URL}?location_id=${storeId}`
      );
    },
    enabled: !!storeId,
    staleTime: 0,
  });
};
