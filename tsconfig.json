{"compilerOptions": {"target": "ES2020", "useDefineForClassFields": true, "lib": ["ES2020", "DOM", "DOM.Iterable"], "module": "ESNext", "skipLibCheck": true, "moduleResolution": "bundler", "allowImportingTsExtensions": true, "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "jsx": "react-jsx", "strict": false, "noUnusedLocals": false, "noUnusedParameters": false, "noFallthroughCasesInSwitch": true, "allowSyntheticDefaultImports": true, "esModuleInterop": true, "forceConsistentCasingInFileNames": true, "baseUrl": ".", "paths": {"@/*": ["src/*"], "@components/*": ["src/components/*"], "@services/*": ["src/services/*"], "@hooks/*": ["src/hooks/*"], "@utils/*": ["src/utils/*"], "@types/*": ["src/types/*"], "@store/*": ["src/store/*"], "@theme/*": ["src/theme/*"]}}, "include": ["src"], "references": [{"path": "./tsconfig.node.json"}]}