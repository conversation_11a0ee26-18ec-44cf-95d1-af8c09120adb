import { useState, useRef, useEffect } from "react";
import {
  X,
  Upload,
  File,
  CheckCircle,
  AlertCircle,
  Trash2,
  FolderOpen,
  CloudUploadIcon,
  FileText,
} from "lucide-react";
import {
  uploadPreSignedUrl,
  usePresignedUrl,
  useCheckBatchAvailabilityStatus,
} from "@/hooks/batchFilesUploads/batchFilesUploads";
import { useToast } from "@/hooks/useToast";
import { Button } from "../ui/button";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Progress } from "@/components/ui/progress";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent } from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { ToastContainer } from "../common/Toast";

interface UploadFile {
  id: string;
  file: File;
  status: "pending" | "uploading" | "completed" | "error";
  progress: number;
  error?: string;
}

interface UploadFilesModalProps {
  isOpen: boolean;
  onClose: () => void;
  onPopUpClose: () => void;
  onUploadComplete: any;
}

export default function UploadFilesModal({
  isOpen,
  onClose,
  onUploadComplete,
  onPopUpClose,
}: UploadFilesModalProps) {
  const [uploadFiles, setUploadFiles] = useState<UploadFile[]>([]);
  const [batchName, setBatchName] = useState("");
  const [selectedStore, setSelectedStore] = useState("1");
  const [description, setDescription] = useState("");
  const [isDragging, setIsDragging] = useState(false);
  const [isUploading, setIsUploading] = useState(false);
  const [overallProgress, setOverallProgress] = useState(0);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [uploadStatusApiCall, setUploadStatusApiCall] = useState(false);
  const [batchId, setBatchId] = useState<string | null>(null);
  const [isCheckingAvailability, setIsCheckingAvailability] = useState(false);
  const [showAvailabilityError, setShowAvailabilityError] = useState(false);
  const [pollAttempts, setPollAttempts] = useState(0);
  const { toasts, removeToast, success, error } = useToast();
  const [stores, setStores] = useState([]);

  const allowedFileTypes = [".csv", ".xlsx", ".xls"];

  const maxFileSize = 25 * 1024 * 1024; // 10MB

  useEffect(() => {
    if (uploadFiles) {
      const formattedStores = uploadFiles?.map((fileObj, index) => ({
        id: String(index + 1), // id as index (starting from 1)
        name: fileObj.file.name, // name from uploaded file's id
      }));
      setStores(formattedStores);
    }
  }, [uploadFiles]);

  const validateFile = (file: File): string | null => {
    const extension = "." + file.name.split(".").pop()?.toLowerCase();
    if (!allowedFileTypes.includes(extension)) {
      return `Supported formats: ${allowedFileTypes.join(", ")}`;
    }
    if (file.size > maxFileSize) {
      return `File size exceeds 25MB limit. Current size: ${(file.size / 1024 / 1024).toFixed(1)} MB`;
    }
    return null;
  };

  const handleFileSelect = (files: FileList | null) => {
    if (!files) return;

    const newFiles: UploadFile[] = [];
    for (let i = 0; i < files.length; i++) {
      const file = files[i];
      const error = validateFile(file);

      // Check for duplicates
      const isDuplicate = uploadFiles.some(
        (uf) => uf.file.name === file.name && uf.file.size === file.size
      );
      if (isDuplicate) continue;

      newFiles.push({
        id: Date.now().toString() + i,
        file,
        status: error ? "error" : "pending",
        progress: 0,
        error,
      });
    }

    setUploadFiles((prev) => [...prev, ...newFiles]);

    // ✅ Clear the input so same file can be reselected
    if (fileInputRef.current) {
      fileInputRef.current.value = "";
    }
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(true);
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(false);
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(false);
    handleFileSelect(e.dataTransfer.files);
  };

  const removeFile = (fileId: string) => {
    setUploadFiles((prev) => prev.filter((f) => f.id !== fileId));
  };

  const { mutate: getPresignedUrl } = usePresignedUrl(
    (data) => {
      // Store batch ID from response
      const batchIdFromResponse = data?.result?.data?.batch_id;
      if (batchIdFromResponse) {
        setBatchId(batchIdFromResponse.toString());
      }

      const getPresignedUrls = data?.result?.data?.files;
      getPresignedUrls.forEach(async ({ upload_url }, index) => {
        const file = uploadFiles[index];
        if (file) {
          const uploadFileAndUrl: any = {
            upload_url: upload_url,
            file: file,
          };
          await uploadFileUsingPresignedUrl(uploadFileAndUrl);
        } else {
          error(
            "Upload Error",
            `No corresponding file found for upload_url ${upload_url}`
          );
        }
      });

      setUploadStatusApiCall(true);
    },
    (errorRes: any) => {
      setIsLoading(false);
      setUploadFiles([]);
      setBatchName("");
      setSelectedStore("1");
      setDescription("");
      setIsUploading(false);
      setOverallProgress(0);
      setIsLoading(false);
      setIsCheckingAvailability(false);
      setShowAvailabilityError(false);
      setBatchId(null);
      setUploadStatusApiCall(false);
      setPollAttempts(0);
      const errorInfo =
        (errorRes as any)?.error?.message ||
        "Something went wrong. Please try again.";
      error("Upload Error", errorInfo);
    }
  );

  // Batch availability checking hook
  const { mutate: checkBatchAvailability } = useCheckBatchAvailabilityStatus(
    (data) => {
      const status = data?.result?.data?.status || data?.data?.status;
      const currentAttempts = pollAttempts + 1;
      setPollAttempts(currentAttempts);

      if (status === true) {
        // Files are available on S3, proceed with success flow
        setIsCheckingAvailability(false);
        setIsLoading(false);
        setPollAttempts(0); // Reset counter
        success("Success", `Files uploaded and available on S3`);
        handleClose();
        onUploadComplete(true);
      } else if (currentAttempts >= 20) {
        // Maximum attempts reached, show error
        setShowAvailabilityError(true);
        setIsUploading(false);
        setIsCheckingAvailability(false);
        setIsLoading(false);
        setPollAttempts(0); // Reset counter
        error(
          "Error",
          "Something went wrong. Files are taking too long to be available on S3. Please try again."
        );
      } else {
        // Files not yet available, continue polling
        setTimeout(() => {
          if (batchId) {
            checkBatchAvailability(batchId);
          }
        }, 2000); // Poll every 2 seconds
      }
    },
    (errorRes) => {
      setIsCheckingAvailability(false);
      setIsLoading(false);
      setPollAttempts(0); // Reset counter
      error("Error", `Failed to check batch availability: ${errorRes}`);
    }
  );

  const { mutate: uploadFileUsingPresignedUrl } = uploadPreSignedUrl(
    (data) => {
      // After successful upload, start checking batch availability
      if (batchId) {
        setIsCheckingAvailability(true);
        setPollAttempts(0); // Reset counter before starting
        checkBatchAvailability(batchId);
        success("Success", `File uploaded successfully to presigned URL`);
      }
    },
    (errorRes) => {
      setIsLoading(false);
      error("Error", `Failed to uploaded file on presigned URL: ${errorRes}`);
    }
  );

  const startUpload = async () => {
    if (!uploadFiles) {
      alert("Select a file first");
      return;
    }
    if (uploadFiles.length === 0 || !batchName.trim()) return;

    setIsUploading(true);
    setOverallProgress(0);

    const payloadData: any = {
      batch_name: batchName,
      files: uploadFiles.map((item) => item.file.name),
    };
    getPresignedUrl(payloadData);
    // const validFiles = uploadFiles.filter((f) => f.status !== "error");

    // // Simulate upload process
    // for (let i = 0; i < validFiles.length; i++) {
    //   const file = validFiles[i];

    //   // Update file status to uploading
    //   setUploadFiles((prev) =>
    //     prev.map((f) =>
    //       f.id === file.id ? { ...f, status: "uploading" as const } : f
    //     )
    //   );

    //   // Simulate upload progress
    //   for (let progress = 0; progress <= 100; progress += 10) {
    //     await new Promise((resolve) => setTimeout(resolve, 100));

    //     setUploadFiles((prev) =>
    //       prev.map((f) => (f.id === file.id ? { ...f, progress } : f))
    //     );

    //     // Update overall progress
    //     const fileProgress = (i * 100 + progress) / validFiles.length;
    //     setOverallProgress(fileProgress);
    //   }

    //   // Mark file as completed
    //   setUploadFiles((prev) =>
    //     prev.map((f) =>
    //       f.id === file.id
    //         ? { ...f, status: "completed" as const, progress: 100 }
    //         : f
    //     )
    //   );
    // }

    // // Simulate a brief delay then complete
    // await new Promise((resolve) => setTimeout(resolve, 500));

    // // CHANGED: Send batchName and files to parent
    // onUploadComplete({
    //   batchName,
    //   files: validFiles.map((f) => ({
    //     id: f.id,
    //     fileName: f.file.name,
    //     fileSize: formatFileSize(f.file.size),
    //     status: "pending",
    //   })),
    // });
    // handleClose();
  };

  const handleClose = () => {
    setUploadFiles([]);
    setBatchName("");
    setSelectedStore("1");
    setDescription("");
    setIsUploading(false);
    setOverallProgress(0);
    setIsLoading(false);
    setIsCheckingAvailability(false);
    setShowAvailabilityError(false);
    setBatchId(null);
    setUploadStatusApiCall(false);
    setPollAttempts(0);
    setTimeout(() => {
      onClose();
    }, 1000);
  };

  const handlePopUpClose = () => {
    setUploadFiles([]);
    setBatchName("");
    setSelectedStore("1");
    setDescription("");
    setIsUploading(false);
    setOverallProgress(0);
    setIsLoading(false);
    setIsCheckingAvailability(false);
    setShowAvailabilityError(false);
    setBatchId(null);
    setUploadStatusApiCall(false);
    setPollAttempts(0);
    onPopUpClose();
  };

  const getFileIcon = (fileName: string) => {
    const extension = fileName.split(".").pop()?.toLowerCase();
    return (
      <FileText
        size={18}
        style={{
          color:
            extension === "csv"
              ? "#71717B"
              : extension === "xlsx" || extension === "xls"
                ? "#71717B"
                : extension === "json"
                  ? "#2563eb"
                  : "#71717a",
        }}
      />
    );
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return "0 Bytes";
    const k = 1024;
    const sizes = ["Bytes", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + " " + sizes[i];
  };

  const validFiles = uploadFiles.filter((f) => f.status !== "error");
  const canUpload =
    validFiles.length > 0 &&
    batchName.trim() &&
    !isUploading &&
    !isCheckingAvailability;

  if (!isOpen) return null;

  return (
    <Dialog open={isOpen} onOpenChange={handlePopUpClose}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-auto">
        <DialogHeader>
          <DialogTitle className="text-lg font-semibold text-zinc-900">
            Upload Supply Chain Data
          </DialogTitle>
        </DialogHeader>

        {/* Batch Information */}
        <div className="">
          <div className="space-y-4 p-4 pt-0">
            <div>
              <Label
                htmlFor="batchName"
                className="text-sm font-medium text-zinc-700"
              >
                Batch Name<span className="text-red-500">*</span>
              </Label>
              <Input
                id="batchName"
                type="text"
                value={batchName}
                onChange={(e) => setBatchName(e.target.value)}
                disabled={isUploading || isCheckingAvailability}
                placeholder="e.g. Jan_Batch_2025"
                className="mt-1"
              />
            </div>

            {/* File Upload Area */}
            <div
              onDragOver={handleDragOver}
              onDragLeave={handleDragLeave}
              onDrop={handleDrop}
              onClick={() =>
                !(isUploading || isCheckingAvailability) &&
                fileInputRef.current?.click()
              }
              className={`
              border-2 border-dashed rounded-lg p-8 text-center transition-all cursor-pointer
              ${isDragging ? "border-emerald-700 bg-green-50" : "border-zinc-200"}
              ${isUploading || isCheckingAvailability ? "cursor-not-allowed" : ""}
            `}
            >
              <CloudUploadIcon
                size={48}
                className="mx-auto mb-4 text-gray-500"
              />
              <h4 className="text-lg font-semibold text-zinc-900 mb-2">
                {isDragging ? (
                  "Drop files here"
                ) : (
                  <span>
                    <span className="text-amber-700">Click to Upload</span> or
                    Drag & Drop
                  </span>
                )}
              </h4>
              <p className="text-gray-500 mb-2">
                Supported formats: CSV, XLSX ,XLS{" "}
              </p>
              <p className="text-gray-500 mb-4">File size limit : 25MB</p>
              <input
                ref={fileInputRef}
                type="file"
                multiple
                accept={allowedFileTypes.join(",")}
                onChange={(e) => handleFileSelect(e.target.files)}
                className="hidden"
                disabled={isUploading || isCheckingAvailability}
              />
            </div>

            {/* Upload Progress */}
            {isUploading && !isCheckingAvailability && (
              <Card className="bg-slate-50">
                <CardContent className="p-6">
                  <div className="flex justify-between text-sm text-gray-500 mb-2">
                    <span>Uploading Files</span>
                    <span>{Math.round(overallProgress)}%</span>
                  </div>
                  <Progress value={overallProgress} className="mb-2" />
                  {/* <div className="text-xs text-gray-500">
                    Uploading
                   {validFiles.length} file
                    {validFiles.length !== 1 ? "s" : ""} - {" "}
                    {stores.find((s) => s.id === selectedStore)?.name}... 
                  </div> */}
                </CardContent>
              </Card>
            )}

            {/* Batch Availability Checking Progress */}
            {isCheckingAvailability && (
              <Card className="bg-blue-50">
                <CardContent className="p-6">
                  <div className="flex justify-between text-sm text-blue-700 mb-2">
                    <span>Verifying... </span>
                    <div className="w-4 h-4 border-2 border-blue-300 border-t-blue-700 rounded-full animate-spin" />
                  </div>
                </CardContent>
              </Card>
            )}

            {showAvailabilityError && (
              <Card className="bg-blue-50">
                <CardContent className="p-6">
                  <div className="flex justify-between text-sm text-red-700 mb-2">
                    <span>Something went wrong while Uploading !</span>
                  </div>
                </CardContent>
              </Card>
            )}

            {/* File List */}
            {uploadFiles.length > 0 && (
              <div>
                <h3 className="text-lg font-semibold text-zinc-900 mb-3">
                  Selected Files ({uploadFiles.length})
                </h3>
                <div className="max-h-[300px] overflow-auto space-y-3">
                  {uploadFiles.map((uploadFile) => (
                    <Card key={uploadFile.id}>
                      <CardContent className="p-4">
                        <div className="flex justify-between items-center">
                          <div className="flex items-center gap-3 flex-1">
                            {getFileIcon(uploadFile.file.name)}
                            <div className="flex-1">
                              <div className="text-sm font-medium text-zinc-900">
                                {uploadFile.file.name}
                              </div>
                              <div className="text-xs text-gray-500">
                                {formatFileSize(uploadFile.file.size)}
                              </div>
                            </div>
                          </div>

                          <div className="flex items-center gap-3">
                            {/* Status Icons */}
                            {/* {uploadFile.status === "pending" && (
                              <div className="w-5 h-5 rounded-full bg-gray-200" />
                            )} */}
                            {uploadFile.status === "uploading" && (
                              <div className="w-4 h-4 border-2 border-gray-300 border-t-emerald-700 rounded-full animate-spin" />
                            )}
                            {uploadFile.status === "completed" && (
                              <CheckCircle
                                size={20}
                                className="text-green-600"
                              />
                            )}
                            {uploadFile.status === "error" && (
                              <AlertCircle size={20} className="text-red-600" />
                            )}

                            {/* Progress or Remove Button */}
                            {uploadFile.status === "uploading" && (
                              <div className="text-xs text-gray-500 min-w-[3rem]">
                                {uploadFile.progress}%
                              </div>
                            )}
                            {(uploadFile.status === "pending" ||
                              uploadFile.status === "error") &&
                              !(
                                isUploading ||
                                isCheckingAvailability ||
                                showAvailabilityError
                              ) && (
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => removeFile(uploadFile.id)}
                                  className="p-1 h-auto hover:bg-white"
                                  title="Remove file"
                                >
                                  <Trash2 size={16} className="text-rose-600" />
                                </Button>
                              )}
                          </div>
                        </div>

                        {/* Error Message */}
                        {uploadFile.error && (
                          <div className="mt-2 p-2 bg-red-50 border border-red-200 rounded text-xs text-red-600">
                            {uploadFile.error}
                          </div>
                        )}

                        {/* Progress Bar */}
                        {uploadFile.status === "uploading" && (
                          <div className="mt-2">
                            <Progress
                              value={uploadFile.progress}
                              className="h-1"
                            />
                          </div>
                        )}
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </div>
            )}
          </div>
          {/* Actions */}
          <div className="flex gap-4 justify-end p-4 border-t">
            <Button
              variant="outline"
              onClick={handlePopUpClose}
              disabled={isUploading || isCheckingAvailability}
              className={
                isUploading || isCheckingAvailability
                  ? "cursor-not-allowed opacity-50"
                  : ""
              }
            >
              Cancel
            </Button>
            <Button
              onClick={startUpload}
              disabled={!canUpload || showAvailabilityError}
              className={`gap-2 ${canUpload ? "bg-primary hover:bg-primary" : ""}`}
            >
              {isUploading ? (
                <>
                  <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin" />
                  Uploading...
                </>
              ) : isCheckingAvailability ? (
                <>
                  <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin" />
                  Verifying...
                </>
              ) : (
                <>
                  <Upload size={14} />
                  Upload Files ({validFiles.length})
                </>
              )}
            </Button>
          </div>
        </div>
      </DialogContent>
      <ToastContainer toasts={toasts} onClose={removeToast} />
    </Dialog>
  );
}
