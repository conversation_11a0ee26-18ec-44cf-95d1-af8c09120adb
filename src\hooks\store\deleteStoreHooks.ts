// hooks/useDeleteStore.ts
import { useMutation } from "@tanstack/react-query";
import useDataService from "@/services/useDataService";
import { STORE_URL } from "@/constants/urls";

interface DeleteStoreResponse {
  success: boolean;
  error?: {
    message: string;
    details: any[];
  };
}

export const useDeleteStore = () => {
  return useMutation<DeleteStoreResponse, Error, number>({
    mutationFn: async (storeId: number) => {
      const response = await useDataService.deleteService(`${STORE_URL}/${storeId}`);
      return response;
    },
  });
};

