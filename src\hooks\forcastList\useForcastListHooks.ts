import useDataService from "../../services/useDataService";
import { GET_FORCAST_LIST_URL } from "../../constants/urls";
import { useQuery } from "@tanstack/react-query";
export const useGetForcastList = (
  page: number,
  size: number,
  filters: { forecastName?: any; store?: any }
) => {
  return useQuery({
    queryKey: ["batch-list", page, size, filters],
    queryFn: async () => {
      let endpoint = `${GET_FORCAST_LIST_URL}?page=${page}&size=${size}`;
      if (filters?.forecastName) {
        endpoint += `&forecast_id=${encodeURIComponent(filters.forecastName)}`;
      }
      if (filters?.store) {
        endpoint += `&location_id=${encodeURIComponent(filters.store)}`;
      }
      return await useDataService.getService(endpoint);
    },
    staleTime: 0,            
  });
};
 
 