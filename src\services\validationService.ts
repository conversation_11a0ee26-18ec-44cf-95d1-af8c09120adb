import { TableData } from '@/data/validationMockData';

interface ApiTableData {
  flashana_schema_id: string;
  uploaded_file_batch_id: string;
  sales_data: any[];
  locations: any[];
  skus: any[];
}

export class ValidationService {
  private static baseUrl = import.meta.env.VITE_API_URL || '/api';

  static async fetchTableData(
    flashanaSchemaId: string,
    uploadedFileBatchId: string
  ): Promise<ApiTableData> {
    try {
      const response = await fetch(
        `${this.baseUrl}/validation/table-data`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            flashana_schema_id: flashanaSchemaId,
            uploaded_file_batch_id: uploadedFileBatchId,
          }),
        }
      );

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      return data;
    } catch (error) {
      console.error('Error fetching table data:', error);
      throw error;
    }
  }

  static async fetchPaginatedData(
    flashanaSchemaId: string,
    uploadedFileBatchId: string,
    tableName: 'sales_data' | 'locations' | 'skus',
    page: number = 1,
    pageSize: number = 10
  ): Promise<{
    data: any[];
    totalItems: number;
    totalPages: number;
    currentPage: number;
  }> {
    try {
      const response = await fetch(
        `${this.baseUrl}/validation/table-data/${tableName}`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            flashana_schema_id: flashanaSchemaId,
            uploaded_file_batch_id: uploadedFileBatchId,
            page,
            page_size: pageSize,
          }),
        }
      );

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      return result;
    } catch (error) {
      console.error('Error fetching paginated data:', error);
      throw error;
    }
  }

  static async downloadValidationReport(
    tableId: string,
    flashanaSchemaId: string,
    uploadedFileBatchId: string
  ): Promise<Blob> {
    try {
      const response = await fetch(
        `${this.baseUrl}/validation/download-report`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            table_id: tableId,
            flashana_schema_id: flashanaSchemaId,
            uploaded_file_batch_id: uploadedFileBatchId,
          }),
        }
      );

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      return await response.blob();
    } catch (error) {
      console.error('Error downloading validation report:', error);
      throw error;
    }
  }

  static async uploadCorrectedFile(
    tableId: string,
    file: File,
    flashanaSchemaId: string,
    uploadedFileBatchId: string
  ): Promise<any> {
    try {
      const formData = new FormData();
      formData.append('file', file);
      formData.append('table_id', tableId);
      formData.append('flashana_schema_id', flashanaSchemaId);
      formData.append('uploaded_file_batch_id', uploadedFileBatchId);

      const response = await fetch(
        `${this.baseUrl}/validation/upload-corrected`,
        {
          method: 'POST',
          body: formData,
        }
      );

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Error uploading corrected file:', error);
      throw error;
    }
  }
}