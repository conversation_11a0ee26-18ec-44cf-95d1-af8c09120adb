import { create } from 'zustand';

interface ForgotPasswordState {
  email: string | null;
  setEmail: (email: string) => void;
  clearEmail: () => void;
}

export const useForgotPasswordStore = create<ForgotPasswordState>((set) => ({
  email: localStorage.getItem('flashana-forgot-email') || null,

  setEmail: (email) => {
    localStorage.setItem('flashana-forgot-email', email);
    set({ email });
  },

  clearEmail: () => {
    localStorage.removeItem('flashana-forgot-email');
    set({ email: null });
  },
}));
