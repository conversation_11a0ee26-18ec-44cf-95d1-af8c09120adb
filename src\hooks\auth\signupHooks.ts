/* eslint-disable @typescript-eslint/no-explicit-any */

import { useMutation, useQueryClient } from "@tanstack/react-query";
import useDataService from "../../services/useDataService";
import { SIGNUP_URL } from "../../constants/urls";

export const usePostSignup = (onPostSuccess: any, onPostError: any) => {
  const queryClient = useQueryClient();
  const {
    mutate: postMutate,
  } = useMutation<any, Error>({
    mutationFn: async (data: any) => {
      const result = await useDataService.postService(`${SIGNUP_URL}`, data);
      return result;
    },
    onSuccess: (data: any) => {
      onPostSuccess(data);
    },
    onSettled: () => {
      queryClient.invalidateQueries({ queryKey: ["signup"] });// You can keep this as a string or key array
    },
    onError: (error: any) => {
        onPostError(error);
    },
  });

  return {postMutate };
};
