import { useState } from "react";
import {
  Calendar,
  Plus,
  Edit,
  Trash2,
  Search,
  Filter,
  MapPin,
  Clock,
} from "lucide-react";
import CreateHolidayModal from "@/components/settings/CreateHolidayModal";
import { useToast } from "@/hooks/useToast";
import { ToastContainer } from "@/components/common/Toast";

interface Holiday {
  id: string;
  name: string;
  date: string;
  type: "national" | "provincial" | "company" | "custom";
  province?: string;
  description?: string;
  isRecurring: boolean;
  affectsForecasting: boolean;
  impact: "high" | "medium" | "low";
}

export default function HolidayListPage() {
  const { toasts, removeToast, success, error } = useToast();
  const [holidays, setHolidays] = useState<Holiday[]>([
    {
      id: "1",
      name: "New Year's Day",
      date: "2024-01-01",
      type: "national",
      description: "National holiday celebrating the beginning of the new year",
      isRecurring: true,
      affectsForecasting: true,
      impact: "high",
    },
    {
      id: "2",
      name: "Family Day",
      date: "2024-02-19",
      type: "provincial",
      province: "Ontario",
      description: "Provincial holiday in Ontario",
      isRecurring: true,
      affectsForecasting: true,
      impact: "medium",
    },
    {
      id: "3",
      name: "Good Friday",
      date: "2024-03-29",
      type: "national",
      description: "Christian holiday commemorating the crucifixion of Jesus",
      isRecurring: true,
      affectsForecasting: true,
      impact: "high",
    },
    {
      id: "4",
      name: "Victoria Day",
      date: "2024-05-20",
      type: "national",
      description: "Celebrates Queen Victoria's birthday",
      isRecurring: true,
      affectsForecasting: true,
      impact: "medium",
    },
    {
      id: "5",
      name: "Canada Day",
      date: "2024-07-01",
      type: "national",
      description: "National holiday celebrating Canadian Confederation",
      isRecurring: true,
      affectsForecasting: true,
      impact: "high",
    },
    {
      id: "6",
      name: "Labour Day",
      date: "2024-09-02",
      type: "national",
      description: "Celebrates the achievements of workers",
      isRecurring: true,
      affectsForecasting: true,
      impact: "medium",
    },
    {
      id: "7",
      name: "Thanksgiving",
      date: "2024-10-14",
      type: "national",
      description: "National day of thanks for the harvest",
      isRecurring: true,
      affectsForecasting: true,
      impact: "high",
    },
    {
      id: "8",
      name: "Christmas Day",
      date: "2024-12-25",
      type: "national",
      description: "Christian holiday celebrating the birth of Jesus",
      isRecurring: true,
      affectsForecasting: true,
      impact: "high",
    },
    {
      id: "9",
      name: "Boxing Day",
      date: "2024-12-26",
      type: "national",
      description: "Traditional shopping holiday after Christmas",
      isRecurring: true,
      affectsForecasting: true,
      impact: "high",
    },
    {
      id: "10",
      name: "Company Anniversary",
      date: "2024-03-15",
      type: "company",
      description: "Annual company founding celebration",
      isRecurring: true,
      affectsForecasting: false,
      impact: "low",
    },
  ]);

  const [searchTerm, setSearchTerm] = useState("");
  const [typeFilter, setTypeFilter] = useState("all");
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [editingHoliday, setEditingHoliday] = useState<Holiday | null>(null);

  const getTypeBadge = (type: string) => {
    const baseStyle = {
      padding: "0.25rem 0.75rem",
      borderRadius: "9999px",
      fontSize: "0.75rem",
      fontWeight: "500",
      textTransform: "capitalize" as const,
    };

    switch (type) {
      case "national":
        return {
          ...baseStyle,
          backgroundColor: "#dcfce7",
          color: "#166534",
          border: "1px solid #bbf7d0",
        };
      case "provincial":
        return {
          ...baseStyle,
          backgroundColor: "#dbeafe",
          color: "#2563eb",
          border: "1px solid #bfdbfe",
        };
      case "company":
        return {
          ...baseStyle,
          backgroundColor: "#f3e8ff",
          color: "#7c3aed",
          border: "1px solid #d8b4fe",
        };
      case "custom":
        return {
          ...baseStyle,
          backgroundColor: "#fef3c7",
          color: "#d97706",
          border: "1px solid #fed7aa",
        };
      default:
        return baseStyle;
    }
  };

  const getImpactBadge = (impact: string) => {
    const baseStyle = {
      padding: "0.25rem 0.5rem",
      borderRadius: "0.375rem",
      fontSize: "0.75rem",
      fontWeight: "500",
      textTransform: "uppercase" as const,
    };

    switch (impact) {
      case "high":
        return { ...baseStyle, backgroundColor: "#fef2f2", color: "#dc2626" };
      case "medium":
        return { ...baseStyle, backgroundColor: "#fef3c7", color: "#d97706" };
      case "low":
        return { ...baseStyle, backgroundColor: "#f0fdf4", color: "#166534" };
      default:
        return baseStyle;
    }
  };

  const filteredHolidays = holidays.filter((holiday) => {
    const matchesSearch =
      holiday.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      holiday.description?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      holiday.province?.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesType = typeFilter === "all" || holiday.type === typeFilter;
    return matchesSearch && matchesType;
  });

  const handleDeleteHoliday = (holidayId: string) => {
    if (window.confirm("Are you sure you want to delete this holiday?")) {
      const deletedHoliday = holidays.find((h) => h.id === holidayId);
      setHolidays(holidays.filter((holiday) => holiday.id !== holidayId));
      success(
        "Holiday Deleted",
        `${deletedHoliday?.name} has been removed from the calendar.`
      );
    }
  };

  const handleCreateHoliday = (data: any) => {
    const newHoliday: Holiday = {
      id: Date.now().toString(),
      ...data,
    };
    setHolidays([...holidays, newHoliday]);
    setShowCreateModal(false);
    success(
      "Holiday Created",
      `${newHoliday.name} has been added to the calendar.`
    );
  };

  const handleEditHoliday = (data: any) => {
    setHolidays(
      holidays.map((holiday) =>
        holiday.id === editingHoliday?.id ? { ...holiday, ...data } : holiday
      )
    );
    setEditingHoliday(null);
    setShowCreateModal(false);
    success("Holiday Updated", `${data.name} has been successfully updated.`);
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString("en-CA", {
      weekday: "long",
      year: "numeric",
      month: "long",
      day: "numeric",
    });
  };

  const isUpcoming = (dateString: string) => {
    const today = new Date();
    const holidayDate = new Date(dateString);
    return holidayDate >= today;
  };

  const daysTillHoliday = (dateString: string) => {
    const today = new Date();
    const holidayDate = new Date(dateString);
    const diffTime = holidayDate.getTime() - today.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays;
  };

  return (
    // <p>Holiday List</p>
    <div style={{ padding: "1.5rem" }}>
      {/* Header */}
      <div
        style={{
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
          marginBottom: "2rem",
        }}
      >
        <div>
          <h1
            style={{
              fontSize: "1.875rem",
              fontWeight: "600",
              color: "#18181b",
              marginBottom: "0.5rem",
            }}
          >
            Holiday Management
          </h1>
          <p style={{ color: "#3f3f46" }}>
            Manage holidays and special dates that affect your business
            forecasting
          </p>
        </div>
        <button
          onClick={() => setShowCreateModal(true)}
          style={{
            display: "flex",
            alignItems: "center",
            gap: "0.5rem",
            backgroundColor: "#2b524f",
            color: "white",
            borderRadius: "0.375rem",
            padding: "0.625rem 1.5rem",
            fontWeight: "500",
            fontSize: "0.875rem",
            border: "none",
            cursor: "pointer",
            transition: "all 0.2s",
          }}
        >
          <Plus size={16} />
          Add Holiday
        </button>
      </div>

      {/* Filters */}
      <div
        style={{
          backgroundColor: "white",
          borderRadius: "0.5rem",
          border: "1px solid #e4e4e7",
          padding: "1.5rem",
          marginBottom: "1.5rem",
        }}
      >
        <div
          style={{
            display: "grid",
            gridTemplateColumns: "2fr 1fr",
            gap: "1rem",
            alignItems: "end",
          }}
        >
          <div>
            <label
              style={{
                display: "block",
                fontSize: "0.875rem",
                fontWeight: "500",
                color: "#3f3f46",
                marginBottom: "0.25rem",
              }}
            >
              Search Holidays
            </label>
            <div style={{ position: "relative" }}>
              <Search
                size={16}
                style={{
                  position: "absolute",
                  left: "0.75rem",
                  top: "50%",
                  transform: "translateY(-50%)",
                  color: "#71717a",
                }}
              />
              <input
                type="text"
                placeholder="Search by name, description, or province..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                style={{
                  width: "100%",
                  paddingLeft: "2.5rem",
                  padding: "0.625rem 0.75rem",
                  border: "1px solid #e4e4e7",
                  borderRadius: "0.375rem",
                  backgroundColor: "white",
                  color: "#18181b",
                  fontSize: "0.875rem",
                  outline: "none",
                }}
              />
            </div>
          </div>

          <div>
            <label
              style={{
                display: "block",
                fontSize: "0.875rem",
                fontWeight: "500",
                color: "#3f3f46",
                marginBottom: "0.25rem",
              }}
            >
              Type
            </label>
            <select
              value={typeFilter}
              onChange={(e) => setTypeFilter(e.target.value)}
              style={{
                width: "100%",
                padding: "0.625rem 0.75rem",
                border: "1px solid #e4e4e7",
                borderRadius: "0.375rem",
                backgroundColor: "white",
                color: "#18181b",
                fontSize: "0.875rem",
                outline: "none",
              }}
            >
              <option value="all">All Types</option>
              <option value="national">National</option>
              <option value="provincial">Provincial</option>
              <option value="company">Company</option>
              <option value="custom">Custom</option>
            </select>
          </div>
        </div>
      </div>

      {/* Holiday Cards */}
      <div style={{ display: "grid", gap: "1rem" }}>
        {filteredHolidays.map((holiday) => (
          <div
            key={holiday.id}
            style={{
              backgroundColor: "white",
              borderRadius: "0.5rem",
              border: "1px solid #e4e4e7",
              padding: "1.5rem",
              transition: "all 0.2s",
              boxShadow: "0 1px 3px 0 rgba(0, 0, 0, 0.1)",
            }}
          >
            <div
              style={{
                display: "flex",
                justifyContent: "space-between",
                alignItems: "start",
              }}
            >
              <div style={{ flex: "1" }}>
                <div
                  style={{
                    display: "flex",
                    alignItems: "center",
                    gap: "1rem",
                    marginBottom: "1rem",
                  }}
                >
                  <Calendar size={20} style={{ color: "#2b524f" }} />
                  <h3
                    style={{
                      fontSize: "1.125rem",
                      fontWeight: "600",
                      color: "#18181b",
                      margin: 0,
                    }}
                  >
                    {holiday.name}
                  </h3>
                  <div style={getTypeBadge(holiday.type)}>{holiday.type}</div>
                  {holiday.affectsForecasting && (
                    <div style={getImpactBadge(holiday.impact)}>
                      {holiday.impact} Impact
                    </div>
                  )}
                  {isUpcoming(holiday.date) && (
                    <div
                      style={{
                        padding: "0.25rem 0.5rem",
                        borderRadius: "0.375rem",
                        fontSize: "0.75rem",
                        fontWeight: "500",
                        backgroundColor: "#e0f2fe",
                        color: "#0369a1",
                      }}
                    >
                      {daysTillHoliday(holiday.date) === 0
                        ? "Today"
                        : daysTillHoliday(holiday.date) === 1
                          ? "Tomorrow"
                          : `${daysTillHoliday(holiday.date)} days`}
                    </div>
                  )}
                </div>

                <div
                  style={{
                    display: "grid",
                    gridTemplateColumns: "repeat(4, 1fr)",
                    gap: "1rem",
                    marginBottom: "1rem",
                  }}
                >
                  <div>
                    <div
                      style={{
                        fontSize: "0.75rem",
                        color: "#71717a",
                        fontWeight: "500",
                      }}
                    >
                      Date
                    </div>
                    <div
                      style={{
                        fontSize: "0.875rem",
                        color: "#18181b",
                        fontWeight: "500",
                      }}
                    >
                      {new Date(holiday.date).toLocaleDateString()}
                    </div>
                    <div style={{ fontSize: "0.75rem", color: "#71717a" }}>
                      {formatDate(holiday.date).split(",")[0]}
                    </div>
                  </div>

                  {holiday.province && (
                    <div>
                      <div
                        style={{
                          fontSize: "0.75rem",
                          color: "#71717a",
                          fontWeight: "500",
                        }}
                      >
                        Province
                      </div>
                      <div
                        style={{
                          fontSize: "0.875rem",
                          color: "#18181b",
                          display: "flex",
                          alignItems: "center",
                          gap: "0.25rem",
                        }}
                      >
                        <MapPin size={12} style={{ color: "#71717a" }} />
                        {holiday.province}
                      </div>
                    </div>
                  )}

                  <div>
                    <div
                      style={{
                        fontSize: "0.75rem",
                        color: "#71717a",
                        fontWeight: "500",
                      }}
                    >
                      Recurring
                    </div>
                    <div style={{ fontSize: "0.875rem", color: "#18181b" }}>
                      {holiday.isRecurring ? "Yes" : "No"}
                    </div>
                  </div>

                  <div>
                    <div
                      style={{
                        fontSize: "0.75rem",
                        color: "#71717a",
                        fontWeight: "500",
                      }}
                    >
                      Forecasting
                    </div>
                    <div
                      style={{
                        fontSize: "0.875rem",
                        color: holiday.affectsForecasting
                          ? "#16a34a"
                          : "#71717a",
                      }}
                    >
                      {holiday.affectsForecasting ? "Affects" : "No Effect"}
                    </div>
                  </div>
                </div>

                {holiday.description && (
                  <p
                    style={{
                      fontSize: "0.875rem",
                      color: "#71717a",
                      margin: 0,
                    }}
                  >
                    {holiday.description}
                  </p>
                )}
              </div>

              <div
                style={{ display: "flex", gap: "0.5rem", marginLeft: "1rem" }}
              >
                <button
                  onClick={() => {
                    setEditingHoliday(holiday);
                    setShowCreateModal(true);
                  }}
                  style={{
                    padding: "0.5rem",
                    color: "#71717a",
                    backgroundColor: "transparent",
                    border: "1px solid #e4e4e7",
                    borderRadius: "0.375rem",
                    cursor: "pointer",
                    transition: "all 0.2s",
                  }}
                  title="Edit Holiday"
                >
                  <Edit size={16} />
                </button>

                <button
                  onClick={() => handleDeleteHoliday(holiday.id)}
                  style={{
                    padding: "0.5rem",
                    color: "#71717a",
                    backgroundColor: "transparent",
                    border: "1px solid #e4e4e7",
                    borderRadius: "0.375rem",
                    cursor: "pointer",
                    transition: "all 0.2s",
                  }}
                  title="Delete Holiday"
                >
                  <Trash2 size={16} />
                </button>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Empty State */}
      {filteredHolidays.length === 0 && (
        <div
          style={{
            textAlign: "center",
            padding: "3rem",
            backgroundColor: "white",
            borderRadius: "0.5rem",
            border: "1px solid #e4e4e7",
          }}
        >
          <Calendar
            size={48}
            style={{ color: "#d1d5db", margin: "0 auto 1rem" }}
          />
          <h3
            style={{
              fontSize: "1.125rem",
              fontWeight: "600",
              color: "#18181b",
              marginBottom: "0.5rem",
            }}
          >
            No holidays found
          </h3>
          <p style={{ color: "#71717a", marginBottom: "1.5rem" }}>
            {searchTerm || typeFilter !== "all"
              ? "Try adjusting your filters to see more results."
              : "Add holidays to help improve forecasting accuracy."}
          </p>
        </div>
      )}

      {/* Summary Stats */}
      <div
        style={{
          display: "grid",
          gridTemplateColumns: "repeat(4, 1fr)",
          gap: "1rem",
          marginTop: "2rem",
        }}
      >
        <div
          style={{
            backgroundColor: "white",
            borderRadius: "0.5rem",
            border: "1px solid #e4e4e7",
            padding: "1rem",
          }}
        >
          <div
            style={{ fontSize: "1.5rem", fontWeight: "600", color: "#2b524f" }}
          >
            {holidays.length}
          </div>
          <div style={{ fontSize: "0.75rem", color: "#71717a" }}>
            Total Holidays
          </div>
        </div>

        <div
          style={{
            backgroundColor: "white",
            borderRadius: "0.5rem",
            border: "1px solid #e4e4e7",
            padding: "1rem",
          }}
        >
          <div
            style={{ fontSize: "1.5rem", fontWeight: "600", color: "#16a34a" }}
          >
            {holidays.filter((h) => h.type === "national").length}
          </div>
          <div style={{ fontSize: "0.75rem", color: "#71717a" }}>National</div>
        </div>

        <div
          style={{
            backgroundColor: "white",
            borderRadius: "0.5rem",
            border: "1px solid #e4e4e7",
            padding: "1rem",
          }}
        >
          <div
            style={{ fontSize: "1.5rem", fontWeight: "600", color: "#2563eb" }}
          >
            {holidays.filter((h) => h.type === "provincial").length}
          </div>
          <div style={{ fontSize: "0.75rem", color: "#71717a" }}>
            Provincial
          </div>
        </div>

        <div
          style={{
            backgroundColor: "white",
            borderRadius: "0.5rem",
            border: "1px solid #e4e4e7",
            padding: "1rem",
          }}
        >
          <div
            style={{ fontSize: "1.5rem", fontWeight: "600", color: "#ab732b" }}
          >
            {holidays.filter((h) => isUpcoming(h.date)).length}
          </div>
          <div style={{ fontSize: "0.75rem", color: "#71717a" }}>Upcoming</div>
        </div>
      </div>

      {/* Create/Edit Holiday Modal */}
      <CreateHolidayModal
        isOpen={showCreateModal}
        onClose={() => {
          setShowCreateModal(false);
          setEditingHoliday(null);
        }}
        onSubmit={editingHoliday ? handleEditHoliday : handleCreateHoliday}
        editingHoliday={editingHoliday}
      />

      {/* Toast Notifications */}
      <ToastContainer toasts={toasts} onClose={removeToast} />
    </div>
  );
}
