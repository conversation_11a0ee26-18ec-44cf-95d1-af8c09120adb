{"fileBatches": {"status": "success", "data": {"batches": [{"id": "batch_001", "name": "January 2025 Sales Data", "status": "imported", "createdAt": "2025-01-15T10:00:00Z", "createdBy": "<PERSON>", "files": [{"id": "file_001", "batchId": "batch_001", "name": "sales_jan_2025.csv", "size": 2048576, "type": "text/csv", "status": "imported", "uploadedAt": "2025-01-15T10:05:00Z", "rowCount": 5000, "columnCount": 12}, {"id": "file_002", "batchId": "batch_001", "name": "inventory_jan_2025.xlsx", "size": 1536000, "type": "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", "status": "imported", "uploadedAt": "2025-01-15T10:06:00Z", "rowCount": 3500, "columnCount": 15}]}, {"id": "batch_002", "name": "Q4 2024 Complete Data", "status": "validated", "createdAt": "2025-01-10T14:30:00Z", "createdBy": "<PERSON>", "files": [{"id": "file_003", "batchId": "batch_002", "name": "q4_2024_sales.csv", "size": 5242880, "type": "text/csv", "status": "validated", "uploadedAt": "2025-01-10T14:35:00Z", "rowCount": 12000, "columnCount": 14}]}, {"id": "batch_003", "name": "Product Master Data Update", "status": "mapping", "createdAt": "2025-01-20T09:00:00Z", "createdBy": "<PERSON>", "files": [{"id": "file_004", "batchId": "batch_003", "name": "products_master.xlsx", "size": 1024000, "type": "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", "status": "uploaded", "uploadedAt": "2025-01-20T09:05:00Z", "rowCount": 450, "columnCount": 20, "columns": ["Product_Code", "Product_Name", "Category", "Brand", "Unit_of_Measure", "Barcode", "Price", "Cost", "Supplier_Code", "Status"]}]}], "total": 3, "page": 1, "pageSize": 10, "totalPages": 1}}, "flashanaSchema": {"status": "success", "data": {"tables": [{"name": "units", "displayName": "Units", "fields": [{"name": "id", "type": "string", "required": true}, {"name": "name", "type": "string", "required": true}, {"name": "abbreviation", "type": "string", "required": true}, {"name": "type", "type": "enum", "required": true, "values": ["volume", "weight", "count", "length"]}]}, {"name": "product_categories", "displayName": "Product Categories", "fields": [{"name": "id", "type": "string", "required": true}, {"name": "name", "type": "string", "required": true}, {"name": "parent_id", "type": "string", "required": false}, {"name": "description", "type": "string", "required": false}]}, {"name": "brands", "displayName": "Brands", "fields": [{"name": "id", "type": "string", "required": true}, {"name": "name", "type": "string", "required": true}, {"name": "description", "type": "string", "required": false}]}, {"name": "products", "displayName": "Products", "fields": [{"name": "id", "type": "string", "required": true}, {"name": "name", "type": "string", "required": true}, {"name": "description", "type": "string", "required": false}, {"name": "category_id", "type": "string", "required": true}, {"name": "brand_id", "type": "string", "required": true}, {"name": "status", "type": "enum", "required": true, "values": ["active", "inactive"]}]}, {"name": "skus", "displayName": "SKUs", "fields": [{"name": "id", "type": "string", "required": true}, {"name": "product_id", "type": "string", "required": true}, {"name": "code", "type": "string", "required": true}, {"name": "name", "type": "string", "required": true}, {"name": "unit", "type": "string", "required": true}, {"name": "barcode", "type": "string", "required": false}, {"name": "status", "type": "enum", "required": true, "values": ["active", "inactive"]}]}, {"name": "suppliers", "displayName": "Suppliers", "fields": [{"name": "id", "type": "string", "required": true}, {"name": "name", "type": "string", "required": true}, {"name": "code", "type": "string", "required": true}, {"name": "address", "type": "string", "required": false}, {"name": "contact_person", "type": "string", "required": false}, {"name": "phone", "type": "string", "required": false}, {"name": "email", "type": "string", "required": false}, {"name": "status", "type": "enum", "required": true, "values": ["active", "inactive"]}]}, {"name": "locations", "displayName": "Locations", "fields": [{"name": "id", "type": "string", "required": true}, {"name": "name", "type": "string", "required": true}, {"name": "type_id", "type": "string", "required": true}, {"name": "address", "type": "string", "required": true}, {"name": "city", "type": "string", "required": true}, {"name": "state", "type": "string", "required": true}, {"name": "zip_code", "type": "string", "required": true}]}, {"name": "sales_data", "displayName": "Sales Data", "fields": [{"name": "id", "type": "string", "required": true}, {"name": "date", "type": "date", "required": true}, {"name": "sku_id", "type": "string", "required": true}, {"name": "location_id", "type": "string", "required": true}, {"name": "quantity", "type": "number", "required": true}, {"name": "unit_price", "type": "number", "required": true}, {"name": "total_price", "type": "number", "required": true}]}]}}, "mappingStatus": {"status": "success", "data": {"tables": [{"tableName": "products", "requiredFields": [{"fieldName": "id", "isMapped": true, "isRequired": true, "dataType": "string"}, {"fieldName": "name", "isMapped": true, "isRequired": true, "dataType": "string"}, {"fieldName": "category_id", "isMapped": false, "isRequired": true, "dataType": "string"}, {"fieldName": "brand_id", "isMapped": false, "isRequired": true, "dataType": "string"}, {"fieldName": "status", "isMapped": true, "isRequired": true, "dataType": "enum"}], "mappedFieldsCount": 3, "totalFieldsCount": 5, "status": "partially_mapped"}, {"tableName": "skus", "requiredFields": [{"fieldName": "id", "isMapped": false, "isRequired": true, "dataType": "string"}, {"fieldName": "product_id", "isMapped": false, "isRequired": true, "dataType": "string"}, {"fieldName": "code", "isMapped": false, "isRequired": true, "dataType": "string"}, {"fieldName": "name", "isMapped": false, "isRequired": true, "dataType": "string"}, {"fieldName": "unit", "isMapped": false, "isRequired": true, "dataType": "string"}], "mappedFieldsCount": 0, "totalFieldsCount": 5, "status": "not_mapped"}], "overallStatus": "partially_mapped"}}, "validationResult": {"status": "success", "data": {"tables": [{"tableName": "products", "totalRows": 450, "validRows": 425, "invalidRows": 25, "errors": [{"row": 15, "field": "category_id", "value": "CAT_999", "error": "Invalid category ID. Category does not exist."}, {"row": 28, "field": "brand_id", "value": null, "error": "Brand ID is required."}, {"row": 45, "field": "status", "value": "discontinued", "error": "Invalid status. Must be 'active' or 'inactive'."}], "status": "partially_validated"}, {"tableName": "skus", "totalRows": 1200, "validRows": 1200, "invalidRows": 0, "errors": [], "status": "validated"}], "overallStatus": "partially_validated"}}, "importedData": {"status": "success", "data": {"tables": ["units", "product_categories", "brands", "products", "skus", "suppliers", "locations", "sales_data"], "batches": [{"id": "batch_001", "name": "January 2025 Sales Data"}, {"id": "batch_002", "name": "Q4 2024 Complete Data"}], "sampleData": {"products": [{"id": "prod_001", "name": "Premium Coffee Beans 1kg", "description": "High-quality arabica coffee beans", "category_id": "cat_001", "brand_id": "brand_001", "status": "active"}, {"id": "prod_002", "name": "Organic Green Tea 500g", "description": "Certified organic green tea leaves", "category_id": "cat_002", "brand_id": "brand_002", "status": "active"}]}}}}