import { ValidationResponse, ValidationResult, ValidationIssue, ValidationSeverity } from '../types';

interface BackendValidationResult {
  employee_id: string;
  check_type: string;
  status: string;
  message: string;
  severity: string;
  expected_value?: any;
  actual_value?: any;
  details?: string;
}

interface BackendValidationResponse {
  session_id: string;
  status?: string;
  compliance_report?: any;
  validation_results: BackendValidationResult[] | any[];
  retrieved_at?: string;
  validation_time_ms?: number;
  message?: string;
}

export const transformValidationResponse = (backendData: BackendValidationResponse): ValidationResponse => {
  // Group validation results by type (payroll, attendance, government)
  const groupedResults: Record<string, ValidationIssue[]> = {
    payroll: [],
    attendance: [],
    government: [],
  };

  // Transform backend results into frontend format
  backendData.validation_results.forEach((result) => {
    // Handle both object and string format
    if (typeof result === 'string') {
      // Skip string representations - we can't parse them reliably
      return;
    }
    
    // Determine file type based on check type
    let fileType = 'general';
    if (result.check_type.includes('payroll') || result.check_type.includes('attendance')) {
      if (result.check_type.includes('payroll') && result.check_type.includes('attendance')) {
        fileType = 'attendance';
      } else if (result.check_type.includes('payroll')) {
        fileType = 'payroll';
      }
    } else if (result.check_type.includes('government')) {
      fileType = 'government';
    }

    // Map severity
    let severity: ValidationSeverity = ValidationSeverity.INFO;
    const severityStr = String(result.severity).toUpperCase();
    if (severityStr.includes('ERROR') || severityStr.includes('CRITICAL')) {
      severity = ValidationSeverity.ERROR;
    } else if (severityStr.includes('WARNING')) {
      severity = ValidationSeverity.WARNING;
    }

    const issue: ValidationIssue = {
      severity,
      message: result.message,
      field: result.employee_id !== 'SYSTEM' ? `Employee ${result.employee_id}` : undefined,
      details: result.details || `Check: ${result.check_type}`,
    };

    // Add to appropriate group
    if (fileType in groupedResults) {
      groupedResults[fileType].push(issue);
    } else {
      // If we can't determine the type, add to all groups
      Object.keys(groupedResults).forEach((key) => {
        groupedResults[key].push(issue);
      });
    }
  });

  // Create validation results for each file type
  const validationResults: ValidationResult[] = [
    {
      file_type: 'payroll',
      issues: groupedResults.payroll,
    },
    {
      file_type: 'attendance',
      issues: groupedResults.attendance,
    },
    {
      file_type: 'government',
      issues: groupedResults.government,
    },
  ];

  return {
    session_id: backendData.session_id,
    status: 'completed',
    validation_results: validationResults,
    validation_time_ms: backendData.validation_time_ms || 0,
    compliance_report: backendData.compliance_report || null,
    message: backendData.message || '',
  };
};