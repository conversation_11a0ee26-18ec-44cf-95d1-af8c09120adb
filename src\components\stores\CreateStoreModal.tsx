import { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import * as yup from "yup";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogClose,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Button } from "@/components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Building2, MapPin, Save, X } from "lucide-react";
import { useGetStoreTypes } from "@/hooks/store/storeTypeHooks";
import {
  CreateStorePayload,
  useCreateStore,
  useUpdateStore,
} from "@/hooks/store/createStoreHooks";
import { Country, State, City } from "country-state-city";
import { useToast } from "@/hooks/useToast";
import { ToastContainer } from "../common/Toast";

const schema = yup.object({
  name: yup
    .string()
    .required("Store name is required")
    .test("no-leading-space", "Name cannot start with a space", value => !/^\s/.test(value || "")),

  manager: yup
    .string()
    .test("no-leading-space", "Manager name cannot start with a space", value => !/^\s/.test(value || "")),

  address: yup
    .string()
    .test("no-leading-space", "Address cannot start with a space", value => !/^\s/.test(value || "")),

  postalCode: yup
    .string()
    .test("no-leading-space", "Postal Code cannot start with a space", value => !/^\s/.test(value || "")),

  email: yup
    .string()
    .email("Invalid email")
    .test("no-leading-space", "Email cannot start with a space", value => !/^\s/.test(value || "")),

  city: yup.string(),
  country: yup.string(),
  province: yup.string(),
  type: yup.string().required("Store type is required"),
  status: yup.string(),
  skuCount: yup.string(),
});

export interface CreateStoreFormData {
  name: string;
  address: string;
  country: string;
  city: string;
  province: string;
  postalCode: string;
  manager: string;
  email: string;
  type: string;
  status: string;
  skuCount: string;
}

interface CreateStoreModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (data: CreateStoreFormData) => void;
  editingStore?: any;
}

export default function CreateStoreModal({
  isOpen,
  onClose,
  onSubmit,
  editingStore,
}: CreateStoreModalProps) {
  const [isLoading, setIsLoading] = useState(false);
  const createStoreMutation = useCreateStore();
  const updateStoreMutation = useUpdateStore();
  const [countries, setCountries] = useState(Country.getAllCountries());
  const [states, setStates] = useState([]);
  const [cities, setCities] = useState([]);
  const { toasts, removeToast, success, error } = useToast();
  const [matchedState, setMatchedState] = useState<any>(null);
  const [matchedCity, setMatchedCity] = useState<any>(null);

  const {
    register,
    handleSubmit,
    reset,
    watch,
    setValue,
    formState: { errors },
  } = useForm<CreateStoreFormData>({
    resolver: yupResolver(schema),
    defaultValues: editingStore || {
      name: "",
      address: "",
      country: "",
      city: "",
      province: "",
      postalCode: "",
      manager: "",
      email: "",
      type: "",
      status: "active",
      skuCount: "",
    },
  });

  const typeValue = watch("type");
  const selectedCountryCode = watch("country");
  const selectedStateCode = watch("province");

  useEffect(() => {
    if (selectedCountryCode) {
      const statesData = State.getStatesOfCountry(selectedCountryCode);
      setStates(statesData);
      setValue("province", "");
      setValue("city", "");
      setCities([]);
    }
  }, [selectedCountryCode, setValue]);

  useEffect(() => {
    if (selectedStateCode && selectedCountryCode) {
      const citiesData = City.getCitiesOfState(
        selectedCountryCode,
        selectedStateCode
      );
      setCities(citiesData);
      setValue("city", "");
    }
  }, [selectedStateCode, selectedCountryCode, setValue]);

  useEffect(() => {
    if (editingStore && editingStore?.country && countries.length > 0) {
      const matchedCountry = countries.find(
        (country) => country.isoCode === editingStore.country
      );
      if (matchedCountry) {
        setValue("country", matchedCountry.isoCode);
      }
    }
  }, [editingStore, countries, setValue]);

  useEffect(() => {
    if (editingStore && editingStore?.state && states.length > 0) {
      const matchedState = states.find(
        (state) => state.isoCode === editingStore.state
      );
      if (matchedState) {
        setValue("province", matchedState.isoCode);
      }
    }
  }, [editingStore, states, setValue]);

  useEffect(() => {
    if (editingStore && editingStore?.city && cities.length > 0) {
      const matchedCity = cities.find(
        (city) => city.name === editingStore.city
      );
      if (matchedCity) {
        setValue("city", matchedCity.name);
      }
    }
  }, [editingStore, cities, setValue]);

  const handleFormSubmit = async (formData: CreateStoreFormData) => {
    setIsLoading(true);
    try {
      // Lookup full names from codes
      const selectedCountryObj = countries.find((c) => c.isoCode === formData.country);
      const selectedStateObj = states.find((s) => s.isoCode === formData.province);

      const storePayload: CreateStorePayload = {
        location_name: formData.name,
        location_type_id: Number(formData.type),
        status: formData.status || "active",
        country: selectedCountryObj?.name ?? "",
        state: selectedStateObj?.name ?? "",
        city: formData.city,
        street_address: formData.address ?? "",
        zip_code: formData.postalCode ?? "",
        contact_name: formData.manager ?? "",
        contact_email: formData.email ?? "",
        sku_count: parseInt(formData.skuCount ?? "0", 10)
      };

      let response;
      if (editingStore) {
        response = await updateStoreMutation.mutateAsync({
          ...storePayload,
          location_id: editingStore.location_id
        });
      } else {
        response = await createStoreMutation.mutateAsync(storePayload);
      }

      if (response?.success && response?.code === 200) {
        success("Success", editingStore ? "Store updated successfully" : "Store created successfully");
        setTimeout(() => {
          onSubmit(formData);
          onClose();
          reset();
        }, 2000);
      } else {
        error("Error", response?.message || "Failed to create or update store");
      }
    } catch (err: any) {
      const message =
        err?.response?.data?.message || // Axios style
        err?.data?.message ||           // Other libraries
        err?.error?.message ||                 // Native error
        "Server error occurred";
      error("Error", message);
      console.error("Store mutation error:", err);
    } finally {
      setIsLoading(false);
    }
  };


  const handleClose = () => {
    reset();
    onClose();
  };

  // Call get store type API
  const { data: locationTypesData, isLoading: isLocationTypesLoading } =
    useGetStoreTypes();

  useEffect(() => {
    if (!editingStore || !countries.length) return;

    let matchedCountry = null;
    let matchedState = null;
    let matchedCity = null;

    if (editingStore.country) {
      matchedCountry = countries.find(
        (c) => c.name?.toLowerCase() === editingStore.country?.toLowerCase()
      );

      if (matchedCountry) {
        setValue("country", matchedCountry.isoCode);

        const statesData = State.getStatesOfCountry(matchedCountry.isoCode);
        setStates(statesData);

        // Wait for states to populate in next effect
        matchedState = statesData.find(
          (s) => s.name?.toLowerCase() === editingStore.state?.toLowerCase()
        );
        setMatchedState(matchedState);

        if (matchedState) {
          const citiesData = City.getCitiesOfState(
            matchedCountry.isoCode,
            matchedState.isoCode
          );
          setCities(citiesData);

          matchedCity = citiesData.find(
            (c) => c.name?.toLowerCase() === editingStore.city?.toLowerCase()
          );
          setMatchedCity(matchedCity);
        }
      }
    }

    // Set other fields immediately
    reset({
      name: editingStore.location_name || "",
      address: editingStore.street_address || "",
      postalCode: editingStore.postal_code || "",
      manager: editingStore.contact_name || "",
      email: editingStore.contact_email || "",
      type: editingStore.location_type_id?.toString() || "",
      status: editingStore.status || "active",
      skuCount: editingStore.sku_count?.toString() || "0",
      country: matchedCountry?.isoCode || "",
      // province and city will be set later
      province: "",
      city: "",
    });
  }, [editingStore, countries, reset, setValue]);

  // 2. Once states & cities are set, then set province & city values
  useEffect(() => {
    if (matchedState) {
      setValue("province", matchedState.isoCode);
    }
  }, [states, matchedState]);

  useEffect(() => {
    if (matchedCity) {
      setValue("city", matchedCity.name);
    }
  }, [cities, matchedCity]);

  if (!isOpen) return null;

  return (
    <>
      <Dialog open={isOpen} onOpenChange={onClose}>
        <DialogContent className="max-h-[90vh] max-w-[50%] overflow-auto p-0 [&>button:last-child]:hidden">
          <form onSubmit={handleSubmit(handleFormSubmit)}>
            <DialogHeader>
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <DialogTitle>
                    {editingStore ? "Edit Store" : "Create a Store"}
                  </DialogTitle>
                </div>
                <DialogClose asChild />
              </div>
            </DialogHeader>

            {/* Store Information */}
            <div className="p-6 border-b">
              <h3 className="text-md text-secondary font-semibold mb-4">
                Store Details
              </h3>
              <div className="grid grid-cols-2 gap-4">
                {/* Store Name */}
                <div>
                  <Label htmlFor="name">
                    Store Code / Name<span className="text-red-500">*</span>
                  </Label>
                  <Input
                    id="name"
                    {...register("name")}
                    placeholder="Enter store name"
                    className={errors.name ? "border-destructive" : ""}
                    onInput={(e) => {
                      const input = e.target as HTMLInputElement;
                      if (input.value.startsWith(" ")) {
                        input.value = input.value.replace(/^\s+/, "");
                      }
                    }}
                  />
                  {errors.name && (
                    <p className="mt-1 text-xs text-destructive">
                      {errors.name.message}
                    </p>
                  )}
                </div>

                {/* Store Type */}
                <div>
                  <Label htmlFor="type">
                    Store Type<span className="text-red-500">*</span>
                  </Label>
                  <Select
                    value={watch("type")}
                    onValueChange={(v) =>
                      setValue("type", v, { shouldValidate: true })
                    }
                    disabled={isLocationTypesLoading}
                  >
                    <SelectTrigger>
                      <SelectValue
                        placeholder={
                          isLocationTypesLoading ? "Loading..." : "Select type"
                        }
                      />
                    </SelectTrigger>
                    <SelectContent>
                      {locationTypesData?.result?.data.map((type) => (
                        <SelectItem
                          key={type.location_type_id}
                          value={String(type.location_type_id)}
                        >
                          {type.type_name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  {errors.type && (
                    <p className="mt-1 text-xs text-destructive">
                      {errors.type.message}
                    </p>
                  )}
                </div>

                {/* Approximate SKU Count */}
                <div>
                  <Label htmlFor="skuCount">SKU Count</Label>
                  <Select
                    value={watch("skuCount")}
                    onValueChange={(val) =>
                      setValue("skuCount", val, { shouldValidate: true })
                    }
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select SKU count" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="100">100</SelectItem>
                      <SelectItem value="200">200</SelectItem>
                      <SelectItem value="300">300</SelectItem>
                      <SelectItem value="400">400</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                {/* Status */}
                <div>
                  <Label htmlFor="status">Status</Label>
                  {editingStore ? (
                    <Select
                      defaultValue={editingStore.status || "active"}
                      onValueChange={(v) => setValue("status", v)}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select status" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="active">Active</SelectItem>
                        <SelectItem value="inactive">Inactive</SelectItem>
                      </SelectContent>
                    </Select>
                  ) : (
                    <div className="border rounded px-3 py-2 text-sm bg-gray-100 text-gray-500 cursor-not-allowed select-none">
                      Active
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* Address Information */}
            <div className="p-6 border-b">
              <h3 className="text-md text-secondary font-semibold mb-4">
                Location Details
              </h3>
              <div className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  {/* Country */}
                  <div>
                    <Label htmlFor="country">Country</Label>
                    <Select
                      value={watch("country")}
                      onValueChange={(val) => {
                        setValue("country", val, { shouldValidate: true });
                      }}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select country" />
                      </SelectTrigger>
                      <SelectContent>
                        {countries.map((country) => (
                          <SelectItem
                            key={country.isoCode}
                            value={country.isoCode}
                          >
                            {country.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    {/* State/Province */}
                    <div>
                      <Label htmlFor="province">State / Region</Label>
                      <Select
                        value={watch("province")}
                        onValueChange={(val) => {
                          setValue("province", val, { shouldValidate: true });
                        }}
                        disabled={!states.length}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Select province" />
                        </SelectTrigger>
                        <SelectContent>
                          {states.map((state) => (
                            <SelectItem
                              key={state.isoCode}
                              value={state.isoCode}
                            >
                              {state.name}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    {/* City */}
                    <div>
                      <Label htmlFor="city">City</Label>
                      <Select
                        value={watch("city")}
                        onValueChange={(val) =>
                          setValue("city", val, { shouldValidate: true })
                        }
                        disabled={!cities.length}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Select city" />
                        </SelectTrigger>
                        <SelectContent>
                          {cities.map((city) => (
                            <SelectItem key={city.name} value={city.name}>
                              {city.name}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="address">Street Address</Label>
                    <Input
                      id="address"
                      {...register("address")}
                      placeholder="123 Main Street"
                      onInput={(e) => {
                        const input = e.target as HTMLInputElement;
                        if (input.value.startsWith(" ")) {
                          input.value = input.value.replace(/^\s+/, "");
                        }
                      }}
                    />
                    {errors.address && (
                      <p className="mt-1 text-xs text-destructive">
                        {errors.address.message}
                      </p>
                    )}
                  </div>
                  <div>
                    <Label htmlFor="postalCode">ZIP/Postal Code</Label>
                    <Input
                      id="postalCode"
                      {...register("postalCode")}
                      placeholder="M5V 3A8"
                      onInput={(e) => {
                        const input = e.target as HTMLInputElement;
                        if (input.value.startsWith(" ")) {
                          input.value = input.value.replace(/^\s+/, "");
                        }
                      }}
                    />
                    {errors.postalCode && (
                      <p className="mt-1 text-xs text-destructive">
                        {errors.postalCode.message}
                      </p>
                    )}
                  </div>
                </div>
              </div>
            </div>

            {/* Contact Info */}
            <div className="p-6 border-b">
              <h3 className="text-md text-secondary font-semibold mb-4">
                Store Contact Info
              </h3>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="manager">Store Contact Name</Label>
                  <Input
                    id="manager"
                    {...register("manager")}
                    placeholder="Manager Name"
                    onInput={(e) => {
                      const input = e.target as HTMLInputElement;
                      if (input.value.startsWith(" ")) {
                        input.value = input.value.replace(/^\s+/, "");
                      }
                    }}
                  />
                  {errors.manager && (
                    <p className="mt-1 text-xs text-destructive">
                      {errors.manager.message}
                    </p>
                  )}
                </div>
                <div>
                  <Label htmlFor="email">Contact Email</Label>
                  <Input
                    id="email"
                    type="text"
                    {...register("email")}
                    placeholder="<EMAIL>"
                    onInput={(e) => {
                      const input = e.target as HTMLInputElement;
                      if (input.value.startsWith(" ")) {
                        input.value = input.value.replace(/^\s+/, "");
                      }
                    }}
                  />
                  {errors.email && (
                    <p className="mt-1 text-xs text-destructive">
                      {errors.email.message}
                    </p>
                  )}
                </div>
              </div>
            </div>

            {/* Actions */}
            <div className="flex gap-4 justify-end p-6">
              <Button
                type="button"
                variant="outline"
                onClick={handleClose}
                disabled={isLoading}
              >
                Cancel
              </Button>
              <Button type="submit" disabled={isLoading}>
                {isLoading ? (
                  <>
                    <span className="animate-spin mr-2 inline-block w-4 h-4 border-2 border-white border-t-transparent rounded-full" />
                    {editingStore ? "Updating..." : "Creating..."}
                  </>
                ) : (
                  <>{editingStore ? "Update Store" : "Create Store"}</>
                )}
              </Button>
            </div>
          </form>
        </DialogContent>
        <ToastContainer toasts={toasts} onClose={removeToast} />
      </Dialog>
    </>
  );
}
