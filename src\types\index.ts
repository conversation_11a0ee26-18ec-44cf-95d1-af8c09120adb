// API Response Types
export interface ApiResponse<T> {
  status: 'success' | 'error'
  data?: T
  message?: string
  error?: string
}

// Session Types
export enum SessionStatus {
  CREATED = 'created',
  UPLOADING = 'uploading',
  PROCESSING = 'processing',
  VALIDATING = 'validating',
  COMPLETED = 'completed',
  FAILED = 'failed',
}

export interface Session {
  session_id: string
  created_at: string
  updated_at?: string
  status: SessionStatus
  files?: SessionFile[]
  validation_results?: ValidationResult[]
  metadata?: {
    file_count?: number
    total_issues?: number
  }
}

export interface SessionFile {
  file_id: string
  file_name: string
  file_type: string
  file_size: number
  uploaded_at: string
  status: string
}

export interface SessionCreateResponse {
  status: string
  session_id: string
  message: string
}

// File Upload Types
export interface FileUploadResponse {
  status: string
  session_id: string
  files: {
    payroll: string
    attendance: string
    government: string
  }
  message: string
}

// Processing Types
export interface DataSummary {
  payroll_records: number
  attendance_records: number
  government_records: number
}

export interface FileProcessResponse {
  status: string
  session_id: string
  processing_time_ms: number
  data_summary: DataSummary
  message: string
}

// Validation Types
export enum ValidationSeverity {
  INFO = 'info',
  WARNING = 'warning',
  ERROR = 'error',
  CRITICAL = 'critical',
}

export interface ValidationResult {
  employee_id?: string
  check_type?: string
  status?: 'PASS' | 'FAIL' | 'WARNING'
  message?: string
  severity?: ValidationSeverity
  file_type: string
  issues: ValidationIssue[]
}

export interface ComplianceReport {
  total_employees: number
  passed_validations: number
  failed_validations: number
  compliance_rate: number
  critical_issues: number
  error_issues: number
  warnings?: number
}

export interface ValidationResponse {
  status: string
  session_id: string
  validation_time_ms: number
  compliance_report: ComplianceReport
  validation_results: ValidationResult[]
  message: string
}

// Upload File Types
export interface UploadFile {
  file: File | null
  name: string
  status: 'idle' | 'selected' | 'uploading' | 'uploaded' | 'error'
  error?: string
}

export interface UploadFiles {
  payroll: UploadFile
  attendance: UploadFile
  government: UploadFile
}

// Workflow Step Types
export interface WorkflowStep {
  id: number
  title: string
  description: string
  status: 'pending' | 'active' | 'completed' | 'error'
  icon: string
  color: string
}

// File Types
export enum FileType {
  PAYROLL = 'payroll',
  ATTENDANCE = 'attendance',
  GOVERNMENT = 'government',
}

// Additional Request Types
export interface FileUploadRequest {
  session_id: string
  file_type: FileType
  file: File
}

export interface ProcessFilesRequest {
  session_id: string
}

export interface ProcessFilesResponse {
  message: string
  status: string
  processed_files: number
}

export interface ValidationRequest {
  session_id: string
  validation_types?: string[]
}

// Enhanced Validation Types
export interface ValidationIssue {
  severity: ValidationSeverity
  message: string
  field?: string
  details?: string
  row?: number
  column?: string
}

export interface ExtendedValidationResult {
  file_id?: string
  file_name?: string
  file_type: string
  validation_status?: 'passed' | 'failed'
  total_errors?: number
  total_warnings?: number
  issues: ValidationIssue[]
  validated_at?: string
}