import React from "react";
import { Loader2 } from "lucide-react";
import { cn } from "@/lib/utils";

interface GlobalLoaderProps {
  /**
   * Size of the spinner
   * @default "default"
   */
  size?: "sm" | "default" | "lg" | "xl";
  /**
   * Custom text to display below the spinner
   * @default "Loading..."
   */
  text?: string;
  /**
   * Whether to show the loading text
   * @default true
   */
  showText?: boolean;
  /**
   * Custom className for the container
   */
  className?: string;
  /**
   * Whether to center the loader in the full screen
   * @default true
   */
  fullScreen?: boolean;
  /**
   * Custom color for the spinner
   * @default "primary"
   */
  variant?: "primary" | "secondary" | "muted" | "white";
}

const GlobalLoader: React.FC<GlobalLoaderProps> = ({
  size = "default",
  text = "Loading...",
  showText = true,
  className,
  fullScreen = true,
  variant = "primary",
}) => {
  const sizeClasses = {
    sm: "w-4 h-4",
    default: "w-6 h-6",
    lg: "w-8 h-8",
    xl: "w-12 h-12",
  };

  const variantClasses = {
    primary: "text-primary",
    secondary: "text-secondary",
    muted: "text-muted-foreground",
    white: "text-white",
  };

  const containerClasses = fullScreen
    ? "flex justify-center items-center "
    : "flex justify-center items-center";

  return (
    <div className="flex flex-col justify-center items-center h-full">
      <div className={cn(containerClasses, className)}>
        <div className="flex flex-col items-center gap-3">
          <Loader2
            className={cn(
              "animate-spin",
              sizeClasses[size],
              variantClasses[variant]
            )}
          />
          {showText && (
            <p
              className={cn(
                "text-sm font-medium",
                variantClasses[variant]
              )}
            >
              {text}
            </p>
          )}
        </div>
      </div>
    </div>
  );
};

export default GlobalLoader;
