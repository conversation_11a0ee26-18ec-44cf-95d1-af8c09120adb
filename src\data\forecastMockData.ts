export interface ForecastDataPoint {
  date: string;
  actual?: number;
  predicted: number;
  confidence_lower: number;
  confidence_upper: number;
}

export interface ForecastModel {
  id: string;
  name: string;
  type: 'linear_regression' | 'arima' | 'lstm' | 'prophet';
  accuracy: number;
  description: string;
  isActive: boolean;
  metrics: {
    mae: number;
    rmse: number;
    mape: number;
    r2: number;
  };
}

export interface ForecastScenario {
  id: string;
  name: string;
  description: string;
  assumptions: string[];
  impact: 'positive' | 'negative' | 'neutral';
  adjustmentFactor: number;
}

// Generate realistic historical data with seasonal patterns
const generateHistoricalData = (): ForecastDataPoint[] => {
  const data: ForecastDataPoint[] = [];
  const baseValue = 25000;
  const startDate = new Date('2024-01-01');
  
  for (let i = 0; i < 30; i++) {
    const date = new Date(startDate);
    date.setDate(date.getDate() + i);
    
    // Add seasonal patterns
    const dayOfWeek = date.getDay();
    const weekendBoost = dayOfWeek === 5 || dayOfWeek === 6 ? 1.2 : 1.0; // Friday/Saturday boost
    const mondayDip = dayOfWeek === 1 ? 0.85 : 1.0; // Monday dip
    
    // Add monthly trend
    const monthlyTrend = 1 + (i / 30) * 0.15; // 15% growth over month
    
    // Add random variation
    const randomVariation = 0.9 + Math.random() * 0.2;
    
    const actual = Math.round(baseValue * weekendBoost * mondayDip * monthlyTrend * randomVariation);
    const predicted = Math.round(actual * (0.95 + Math.random() * 0.1)); // ±5% prediction accuracy
    
    data.push({
      date: date.toISOString().split('T')[0],
      actual,
      predicted,
      confidence_lower: Math.round(predicted * 0.9),
      confidence_upper: Math.round(predicted * 1.1)
    });
  }
  
  return data;
};

// Generate forecast data with realistic patterns
const generateForecastData = (historicalData: ForecastDataPoint[], days: number = 30): ForecastDataPoint[] => {
  const data: ForecastDataPoint[] = [];
  const lastHistorical = historicalData[historicalData.length - 1];
  const lastDate = new Date(lastHistorical.date);
  const baseValue = lastHistorical.actual || lastHistorical.predicted;
  
  for (let i = 1; i <= days; i++) {
    const date = new Date(lastDate);
    date.setDate(date.getDate() + i);
    
    // Seasonal patterns
    const dayOfWeek = date.getDay();
    const weekendBoost = dayOfWeek === 5 || dayOfWeek === 6 ? 1.2 : 1.0;
    const mondayDip = dayOfWeek === 1 ? 0.85 : 1.0;
    
    // Growth trend with some volatility
    const growthTrend = 1 + (i / days) * 0.12; // 12% growth projection
    const volatility = 0.95 + Math.random() * 0.1;
    
    // Confidence decreases over time
    const confidenceDecay = Math.max(0.7, 1 - (i / days) * 0.3);
    
    const predicted = Math.round(baseValue * weekendBoost * mondayDip * growthTrend * volatility);
    const confidenceRange = predicted * (0.15 / confidenceDecay); // Wider range further out
    
    data.push({
      date: date.toISOString().split('T')[0],
      predicted,
      confidence_lower: Math.round(predicted - confidenceRange),
      confidence_upper: Math.round(predicted + confidenceRange)
    });
  }
  
  return data;
};

export const historicalRevenueData = generateHistoricalData();
export const forecastRevenueData = generateForecastData(historicalRevenueData, 30);

// Product-level forecast data
export const productForecastData = {
  'electronics': {
    historical: generateHistoricalData().map(d => ({ ...d, actual: d.actual ? d.actual * 0.4 : undefined, predicted: d.predicted * 0.4 })),
    forecast: generateForecastData(historicalRevenueData, 30).map(d => ({ ...d, predicted: d.predicted * 0.4 }))
  },
  'clothing': {
    historical: generateHistoricalData().map(d => ({ ...d, actual: d.actual ? d.actual * 0.3 : undefined, predicted: d.predicted * 0.3 })),
    forecast: generateForecastData(historicalRevenueData, 30).map(d => ({ ...d, predicted: d.predicted * 0.3 }))
  },
  'home_garden': {
    historical: generateHistoricalData().map(d => ({ ...d, actual: d.actual ? d.actual * 0.2 : undefined, predicted: d.predicted * 0.2 })),
    forecast: generateForecastData(historicalRevenueData, 30).map(d => ({ ...d, predicted: d.predicted * 0.2 }))
  },
  'sports': {
    historical: generateHistoricalData().map(d => ({ ...d, actual: d.actual ? d.actual * 0.1 : undefined, predicted: d.predicted * 0.1 })),
    forecast: generateForecastData(historicalRevenueData, 30).map(d => ({ ...d, predicted: d.predicted * 0.1 }))
  }
};

// Store-level forecast data
export const storeForecastData = {
  'toronto_downtown': {
    historical: historicalRevenueData.map(d => ({ ...d, actual: d.actual ? d.actual * 0.5 : undefined, predicted: d.predicted * 0.5 })),
    forecast: forecastRevenueData.map(d => ({ ...d, predicted: d.predicted * 0.5 }))
  },
  'mississauga_warehouse': {
    historical: historicalRevenueData.map(d => ({ ...d, actual: d.actual ? d.actual * 0.3 : undefined, predicted: d.predicted * 0.3 })),
    forecast: forecastRevenueData.map(d => ({ ...d, predicted: d.predicted * 0.3 }))
  },
  'vancouver_outlet': {
    historical: historicalRevenueData.map(d => ({ ...d, actual: d.actual ? d.actual * 0.2 : undefined, predicted: d.predicted * 0.2 })),
    forecast: forecastRevenueData.map(d => ({ ...d, predicted: d.predicted * 0.2 }))
  }
};

export const forecastModels: ForecastModel[] = [
  {
    id: 'lr1',
    name: 'Linear Regression',
    type: 'linear_regression',
    accuracy: 82.5,
    description: 'Simple linear trend analysis for baseline forecasting',
    isActive: false,
    metrics: {
      mae: 1845.6,
      rmse: 2376.3,
      mape: 6.8,
      r2: 0.825
    }
  },
  {
    id: 'arima1',
    name: 'ARIMA (2,1,2)',
    type: 'arima',
    accuracy: 89.3,
    description: 'Autoregressive Integrated Moving Average for time series analysis',
    isActive: true,
    metrics: {
      mae: 1245.6,
      rmse: 1876.3,
      mape: 4.2,
      r2: 0.893
    }
  },
  {
    id: 'lstm1',
    name: 'LSTM Neural Network',
    type: 'lstm',
    accuracy: 94.7,
    description: 'Deep learning model for complex pattern recognition',
    isActive: false,
    metrics: {
      mae: 987.2,
      rmse: 1423.1,
      mape: 3.1,
      r2: 0.947
    }
  },
  {
    id: 'prophet1',
    name: 'Facebook Prophet',
    type: 'prophet',
    accuracy: 91.2,
    description: 'Handles seasonality and holidays automatically',
    isActive: false,
    metrics: {
      mae: 1156.8,
      rmse: 1687.4,
      mape: 3.8,
      r2: 0.912
    }
  }
];

export const forecastScenarios: ForecastScenario[] = [
  {
    id: 'optimistic',
    name: 'Optimistic Growth',
    description: 'Best-case scenario with favorable market conditions',
    assumptions: [
      'Economic recovery continues',
      'No supply chain disruptions',
      'Successful marketing campaigns',
      'Competitor market share remains stable'
    ],
    impact: 'positive',
    adjustmentFactor: 1.15
  },
  {
    id: 'conservative',
    name: 'Conservative Baseline',
    description: 'Current trends continue with minimal changes',
    assumptions: [
      'Stable economic conditions',
      'Normal seasonal patterns',
      'Current customer retention rates',
      'No major market disruptions'
    ],
    impact: 'neutral',
    adjustmentFactor: 1.0
  },
  {
    id: 'pessimistic',
    name: 'Market Downturn',
    description: 'Challenging conditions with reduced consumer spending',
    assumptions: [
      'Economic uncertainty increases',
      'Supply chain cost increases',
      'Increased competition',
      'Reduced consumer confidence'
    ],
    impact: 'negative',
    adjustmentFactor: 0.85
  },
  {
    id: 'seasonal_boost',
    name: 'Holiday Season Boost',
    description: 'Enhanced performance during peak shopping periods',
    assumptions: [
      'Strong holiday shopping season',
      'Effective promotional strategies',
      'Inventory availability maintained',
      'Gift-giving trends favor our products'
    ],
    impact: 'positive',
    adjustmentFactor: 1.25
  }
];

// Key performance indicators for forecasting dashboard
export const forecastKPIs = {
  totalRevenueForecast: {
    current: 28500,
    previous: 26800,
    trend: 'up',
    change: 6.3,
    period: 'next 30 days'
  },
  averageOrderValue: {
    current: 127.50,
    previous: 119.80,
    trend: 'up',
    change: 6.4,
    period: 'projected'
  },
  customerAcquisition: {
    current: 145,
    previous: 132,
    trend: 'up',
    change: 9.8,
    period: 'new customers/month'
  },
  conversionRate: {
    current: 3.2,
    previous: 2.9,
    trend: 'up',
    change: 10.3,
    period: 'forecasted %'
  },
  inventoryTurnover: {
    current: 8.5,
    previous: 7.8,
    trend: 'up',
    change: 9.0,
    period: 'times/year projected'
  },
  marketShare: {
    current: 12.8,
    previous: 11.9,
    trend: 'up',
    change: 7.6,
    period: 'estimated %'
  }
};

// Business insights generated from forecast analysis
export const forecastInsights = [
  {
    id: 'seasonal_pattern',
    type: 'trend',
    priority: 'high',
    title: 'Strong Weekend Performance Pattern',
    description: 'Friday and Saturday consistently show 20% higher revenue than weekdays',
    recommendation: 'Increase weekend staffing and inventory allocation',
    impact: 'Revenue optimization',
    confidence: 0.92
  },
  {
    id: 'growth_trajectory',
    type: 'opportunity',
    priority: 'high',
    title: 'Accelerating Growth Trend',
    description: 'Monthly growth rate has increased from 8% to 12% over the past quarter',
    recommendation: 'Consider expanding product lines or store locations',
    impact: 'Strategic expansion',
    confidence: 0.87
  },
  {
    id: 'monday_optimization',
    type: 'improvement',
    priority: 'medium',
    title: 'Monday Revenue Gap',
    description: 'Mondays consistently underperform by 15% compared to weekly average',
    recommendation: 'Implement Monday-specific promotions or marketing campaigns',
    impact: 'Revenue recovery',
    confidence: 0.84
  },
  {
    id: 'demand_forecasting',
    type: 'prediction',
    priority: 'high',
    title: 'Peak Demand Period Approaching',
    description: 'Model predicts 25% increase in demand during weeks 3-4 of forecast period',
    recommendation: 'Increase inventory levels and prepare for higher order volumes',
    impact: 'Inventory management',
    confidence: 0.89
  },
  {
    id: 'model_accuracy',
    type: 'validation',
    priority: 'low',
    title: 'Forecast Model Performance',
    description: 'ARIMA model showing 94.7% accuracy on recent predictions',
    recommendation: 'Continue using current model with periodic recalibration',
    impact: 'Forecast reliability',
    confidence: 0.95
  }
];

// Export helper functions
export const getActiveModel = () => forecastModels.find(model => model.isActive) || forecastModels[1];

export const applyScenarioToForecast = (baseData: ForecastDataPoint[], scenarioId: string): ForecastDataPoint[] => {
  const scenario = forecastScenarios.find(s => s.id === scenarioId);
  if (!scenario) return baseData;
  
  return baseData.map(point => ({
    ...point,
    predicted: Math.round(point.predicted * scenario.adjustmentFactor),
    confidence_lower: Math.round(point.confidence_lower * scenario.adjustmentFactor),
    confidence_upper: Math.round(point.confidence_upper * scenario.adjustmentFactor)
  }));
};

export const generateCustomForecast = (
  period: number,
  targetMetric: string,
  modelType: string
): { historical: ForecastDataPoint[], forecast: ForecastDataPoint[] } => {
  const multiplier = targetMetric === 'revenue' ? 1 : 
                    targetMetric === 'units' ? 0.1 : 
                    targetMetric === 'transactions' ? 0.05 : 0.02;
  
  const historical = generateHistoricalData().map(d => ({
    ...d,
    actual: d.actual ? Math.round(d.actual * multiplier) : undefined,
    predicted: Math.round(d.predicted * multiplier),
    confidence_lower: Math.round(d.confidence_lower * multiplier),
    confidence_upper: Math.round(d.confidence_upper * multiplier)
  }));
  
  const forecast = generateForecastData(historical, period).map(d => ({
    ...d,
    predicted: Math.round(d.predicted * multiplier),
    confidence_lower: Math.round(d.confidence_lower * multiplier),
    confidence_upper: Math.round(d.confidence_upper * multiplier)
  }));
  
  return { historical, forecast };
};