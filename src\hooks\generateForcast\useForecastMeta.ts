import { useEffect, useState } from "react";
import { useGetStoreList } from "../store/storeListHooks";
import { useGetForecastAlgorithms } from "./forecastAlgorithmHooks";
import { useGetForecastFactors } from "./forecastFactorHooks";

export const useForecastMeta = () => {
  const [isMetaLoading, setIsMetaLoading] = useState(true);
  const [meta, setMeta] = useState({
    stores: [],
    algorithms: [],
    factors: [],
    defaultAlgorithm: "", // 👈 add this here
  });

  const {
    data: storeData,
    isLoading: isStoresLoading,
    refetch: refetchStores,
  } = useGetStoreList();

  const {
    data: algoData,
    isLoading: isAlgoLoading,
    refetch: refetchAlgos,
  } = useGetForecastAlgorithms();

  const {
    data: factorData,
    isLoading: isFactorsLoading,
    refetch: refetchFactors,
  } = useGetForecastFactors();

  useEffect(() => {
    const fetchAll = async () => {
      setIsMetaLoading(true);
      try {
        const [storeRes, algoRes, factorRes] = await Promise.all([
          refetchStores(),
          refetchAlgos(),
          refetchFactors(),
        ]);

        const algorithms = algoRes.data?.result?.data || [];
        const defaultAlgo = algorithms.find(
          (a) => a.algorithm_name === "prophet"
        );

        setMeta({
          stores: storeRes.data?.data || [],
          algorithms,
          factors: factorRes.data?.result?.data || [],
          defaultAlgorithm: defaultAlgo?.algorithm_name || "",
        });
      } catch (err) {
        console.error("Error loading metadata:", err);
      } finally {
        setIsMetaLoading(false);
      }
    };

    fetchAll();
  }, []);

  return {
    ...meta,
    isMetaLoading:
      isStoresLoading ||
      isAlgoLoading ||
      isFactorsLoading ||
      isMetaLoading,
  };
};
