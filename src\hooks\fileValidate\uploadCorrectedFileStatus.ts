import { useMutation } from '@tanstack/react-query';
import useDataService from '../../services/useDataService';
import { GET_CORRECTED_FILE_UPLOAD_STATUS } from "../../constants/urls";

export const useCheckUploadCorrectedFileStatus = (
  onSuccess?: (data: any) => void,
  onError?: (error: Error) => void
) => {
  return useMutation({
    mutationFn: async (batchId: string) => {
      const endpoint = `${GET_CORRECTED_FILE_UPLOAD_STATUS + batchId}`;
      return await useDataService.getService(endpoint);
    },
    onSuccess,
    onError,
  });
};