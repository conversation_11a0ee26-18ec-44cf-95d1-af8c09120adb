import { useMutation, useQuery } from '@tanstack/react-query';
import { useSnackbar } from 'notistack';
import { api } from '../services/api';
import { useSessionStore } from '../store/useSessionStore';
import { Session } from '../types';

export const useSession = () => {
  const { enqueueSnackbar } = useSnackbar();
  const { setCurrentSession, addSession } = useSessionStore();

  const createSessionMutation = useMutation<Session, Error>({
    mutationFn: api.createSession,
    onSuccess: (data) => {
      addSession(data);
      enqueueSnackbar('Session created successfully', { variant: 'success' });
    },
    onError: (error) => {
      enqueueSnackbar(`Failed to create session: ${error.message}`, {
        variant: 'error',
      });
    },
  });

  const useSessionDetails = (sessionId?: string) => {
    return useQuery<Session>({
      queryKey: ['session', sessionId],
      queryFn: () => api.getSessionDetails(sessionId!),
      enabled: !!sessionId,
      refetchInterval: 5000, // Poll every 5 seconds for status updates
    });
  };

  return {
    createSession: createSessionMutation.mutate,
    createSessionAsync: createSessionMutation.mutateAsync,
    isCreatingSession: createSessionMutation.isPending,
    useSessionDetails,
    setCurrentSession,
  };
};