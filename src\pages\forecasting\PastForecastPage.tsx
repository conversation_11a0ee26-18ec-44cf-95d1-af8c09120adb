import { useEffect, useRef, useState } from "react";
import { usePara<PERSON>, useNavigate } from "react-router-dom";
import { ArrowLeft, Info } from "lucide-react";
import InteractiveForecastChart from "@/components/forecasting/InteractiveForecastChart";
import {
  historicalRevenueData,
  forecastRevenueData,
} from "@/data/forecastMockData";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useGetForecastResults } from "@/hooks/generateForcast/generateForcastHooks";
import { useToast } from "@/hooks/useToast";
import {
  Table,
  TableHeader,
  TableRow,
  TableHead,
  TableBody,
  TableCell,
} from "@/components/ui/table";
import { useLocation } from "react-router-dom";
import { Button } from "@/components/ui/button";
import ChartJsForecast from "@/components/forecasting/ChartJsForecast";

interface ForecastData {
  date: string;
  productSKU: string;
  store: string;
  actual?: number;
  forecast: number;
  lowerBound: number;
  upperBound: number;
}

// Mock data that includes actual past sales data
const mockPastForecastTableData: ForecastData[] = [
  {
    date: "12-05-24",
    productSKU: "KPF007",
    store: "Toronto",
    actual: 475,
    forecast: 480,
    lowerBound: 455,
    upperBound: 510,
  },
  {
    date: "08-02-25",
    productSKU: "VQ0001",
    store: "Toronto",
    actual: 488,
    forecast: 495,
    lowerBound: 470,
    upperBound: 530,
  },
  {
    date: "11-25-25",
    productSKU: "WH8002",
    store: "Toronto",
    forecast: 510,
    lowerBound: 475,
    upperBound: 545,
  },
];

interface ChartDataPoint {
  date: string;
  actual?: number;
  value: number;
  confidence_lower?: number;
  confidence_upper?: number;
}

const mockHistoricalData: ChartDataPoint[] = [];

export default function PastForecastPage() {
  const { forecastId } = useParams();
  const location = useLocation();
  const forecastData = location.state?.forecast;
  const navigate = useNavigate();
  const [forecastResultData, setForecastResultData] = useState([]);
  const [historicalData, setHistoricalData] = useState([]);
  const [actualsData, setActualsData] = useState([]);
  const [isGenerating, setIsGenerating] = useState(false);
  const { toasts, removeToast, success, error } = useToast();

  console.log("forecastResultData", forecastResultData);
  console.log("historicalData", historicalData);
  console.log("actualsData", actualsData);

  const formattedHistoricalData = mockHistoricalData.map((d) => ({
    date: d.date,
    value: d.value,
  }));

  console.log("Vurrent data", forecastResultData);
  // Format forecast data
  const formattedForecastData = forecastResultData.map((d) => ({
    date: d.forecast_date,
    value: d.forecast_quantity,
    confidence_lower: d.forecast_lower,
    confidence_upper: d.forecast_upper,
  }));

  const { checkResult } = useGetForecastResults(
    (data) => {
      console.log(data);
      const resultArrayForecast = data.result.forecast_data || [];
      const resultArrayHistorical = data.result.historical_data || [];
      const resultArrayActuals = data.result.actuals_data || [];
      // setForecastResultData(resultArrayForecast);
      setHistoricalData(resultArrayHistorical);
      setActualsData(resultArrayActuals);

      console.log("resultArrayForecast", resultArrayForecast);
      const merged = resultArrayForecast.map((forecast) => {
        const matchedActual = resultArrayActuals.find(
          (actual) =>
            new Date(actual.date).toISOString().slice(0, 10) ===
            new Date(forecast.forecast_date).toISOString().slice(0, 10)
        );
        return {
          ...forecast,
          actual: matchedActual ? matchedActual.value : null,
        };
      });

      console.log("Merged Data ", merged);
      setForecastResultData(merged);
    },
    (errorInfo) => {
      error("Error", `${errorInfo}`);
    }
  );

  const hasFetched = useRef(false);

  useEffect(() => {
    if (forecastId && !hasFetched.current) {
      hasFetched.current = true;
      checkResult(forecastId);
    }
  }, [forecastId]);
  // Mock forecast settings based on forecastId
  const forecastSettings = {
    batch: "Jan_Batch_2025",
    algorithm: "Prophet",
    sku: "abc123",
    horizon: "30",
    filterBy: "SKU",
    label: "Store A-Q3-Forecast",
    includeFactors: "Weather",
  };

  const handleBack = () => {
    navigate("/forecasting/list");
  };

  const handleDownloadReport = () => {
    console.log("Downloading forecast report...");

    if (!Array.isArray(forecastResultData) || forecastResultData.length === 0)
      return;

    const csvHeader =
      "Date, Product/SKU, Store, Actual Sales, Forecast (y), Lower Bound (y-lower), Upper Bound (y-upper)\n";

    const csvRows = forecastResultData.map((row) => {
      const formattedDate = row.forecast_date
        ? new Date(row.forecast_date).toLocaleDateString("en-US", {
            month: "2-digit",
            day: "2-digit",
            year: "numeric",
          })
        : "-";

      const actual =
        row.actual !== undefined && row.actual !== null ? row.actual : "-";

      return `"${formattedDate}",${row.sku_code},"${row.location_name}","${actual}",${row.forecast_quantity},${row.forecast_lower},${row.forecast_upper}`;
    });

    const csvContent = csvRows.join("\n");
    const blob = new Blob([csvHeader + csvContent], { type: "text/csv" });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = url;
    a.download = `forecast_${forecastData?.name}_${new Date().toISOString().split("T")[0]}.csv`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    window.URL.revokeObjectURL(url);
  };

  // // Combine historical and forecast data for the chart
  // const combinedHistoricalData = historicalRevenueData.map((item) => ({
  //   ...item,
  //   actual: item.actual || item.predicted,
  // }));

  // const combinedForecastData = forecastRevenueData.map((item, index) => {
  //   // Mock some actual data for past dates
  //   const isPastDate = index < 10; // First 10 items are past dates with actual data
  //   return {
  //     ...item,
  //     actual: isPastDate
  //       ? item.predicted + (Math.random() - 0.5) * 20
  //       : undefined,
  //   };
  // });

  return (
    <>
      <div className="mb-2 flex items-center justify-between">
        <div className="flex items-center gap-4">
          <button
            onClick={handleBack}
            className="p-2 hover:bg-muted rounded-lg transition-colors"
          >
            <ArrowLeft className="h-5 w-5" />
          </button>
          <h1 className="font-medium">Back</h1>
        </div>
      </div>

      {/* Forecast Settings Display */}
      <Card className="mb-6">
        <CardContent className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div>
              <Label
                htmlFor="label"
                className="mb-2 block text-zinc-600 font-normal"
              >
                Forecast Label
              </Label>
              <Input
                id="label"
                value={forecastData.name}
                readOnly
                className="text-md font-medium p-0 border-hidden focus:outline-none focus:ring-0"
              />
            </div>
            {/* <div>
              <Label
                htmlFor="batch"
                className="mb-2 block text-zinc-600 font-normal"
              >
                Selected Batch
              </Label>
              <Input
                id="batch"
                value={forecastSettings.batch}
                readOnly
                className="text-md font-medium p-0 border-hidden"
              />
            </div> */}
            <div>
              <Label
                htmlFor="algorithm"
                className="mb-2 block text-zinc-600 font-normal"
              >
                Algorithm Used
              </Label>
              <Input
                id="algorithm"
                value={
                  forecastData.model
                    ? forecastData.model.charAt(0).toUpperCase() +
                      forecastData.model.slice(1)
                    : ""
                }
                readOnly
                className="text-md font-medium p-0 border-hidden focus:outline-none focus:ring-0"
              />
            </div>
            <div>
              <Label
                htmlFor="sku"
                className="mb-2 block text-zinc-600 font-normal"
              >
                SKU Selected
              </Label>
              <Input
                id="sku"
                value={
                  forecastData.sku_codes.length > 0
                    ? forecastData.sku_codes.join(", ")
                    : "-"
                }
                readOnly
                className="text-md font-medium p-0 border-hidden focus:outline-none focus:ring-0"
              />
            </div>
            <div>
              <Label
                htmlFor="horizon"
                className="mb-2 block text-zinc-600 font-normal"
              >
                Forecast Horizon (in days)
              </Label>
              <Input
                id="horizon"
                value={forecastData.forecast_days}
                readOnly
                className="text-md font-medium p-0 border-hidden focus:outline-none focus:ring-0"
              />
            </div>
            {/* <div>
              <Label
                htmlFor="filterBy"
                className="mb-2 block text-zinc-600 font-normal"
              >
                Filter by Store / Product / SKU
              </Label>
              <Input
                id="filterBy"
                value={forecastSettings.filterBy}
                readOnly
                className="text-md font-medium p-0 border-hidden"
              />
            </div> */}
            <div>
              <Label
                htmlFor="factors"
                className="mb-2 block text-zinc-600 font-normal"
              >
                Included Factors
              </Label>
              <Input
                id="factors"
                value={
                  forecastData.factors.length > 0
                    ? forecastData.factors.join(", ").charAt(0).toUpperCase() +
                      forecastData.factors.join(", ").slice(1)
                    : "-"
                }
                readOnly
                className="text-md font-medium p-0 border-hidden focus:outline-none focus:ring-0"
              />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Chart Section */}
      <Card className="mb-6">
        <CardHeader>
          <CardTitle className="text-lg font-semibold">
            Forecast Results
          </CardTitle>
        </CardHeader>
        <CardContent>
          {/* Info Label */}
          <div className="pb-4 rounded text-cyan-700 text-sm font-medium flex items-center justify-between">
            <span className="flex gap-2">
              <Info size={20} className="min-w-[1rem]" />{" "}
              <span>
                The more historical data available, the higher the chances of
                achieving better forecast accuracy. Limited historical data may
                result in lower accuracy.
              </span>
            </span>
          </div>
          {/* <InteractiveForecastChart
            historicalData={historicalData}
            forecastData={formattedForecastData}
            actualsData={actualsData?.length > 0 ? actualsData : []}
            isLoading={isGenerating}
          /> */}
          <ChartJsForecast
            historicalData={historicalData}
            forecastData={formattedForecastData}
            isLoading={formattedForecastData ? false : true}
            actualsData={actualsData?.length > 0 ? actualsData : []}
          />
        </CardContent>
      </Card>

      {/* Forecast Table */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg font-semibold">
            Forecast Table
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <Table className="border">
              <TableHeader>
                <TableRow>
                  <TableHead>Date</TableHead>
                  <TableHead>Product/SKU</TableHead>
                  <TableHead>Store</TableHead>
                  <TableHead className="text-center">Actual Sales</TableHead>
                  <TableHead className="text-right">Forecast (y)</TableHead>
                  <TableHead className="text-right">
                    Lower Bound (y-lower)
                  </TableHead>
                  <TableHead className="text-right">
                    Upper Bound (y-upper)
                  </TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {forecastResultData.map((row, index) => (
                  <TableRow key={index} className="hover:bg-muted/50">
                    <TableCell>
                      {row.forecast_date
                        ? new Date(row.forecast_date).toLocaleDateString(
                            "en-US",
                            {
                              month: "2-digit",
                              day: "2-digit",
                              year: "numeric",
                            }
                          )
                        : "-"}
                    </TableCell>
                    <TableCell>{row.sku_code}</TableCell>
                    <TableCell>{row.location_name}</TableCell>
                    <TableCell className="text-center">
                      {row.actual ?? "-"}
                    </TableCell>
                    <TableCell className="text-right">
                      {row.forecast_quantity}
                    </TableCell>
                    <TableCell className="text-right">
                      {row.forecast_lower}
                    </TableCell>
                    <TableCell className="text-right">
                      {row.forecast_upper}
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
          <div className="pt-6 flex justify-end">
            <Button onClick={handleDownloadReport}>
              Download Forecast Table
            </Button>
          </div>
        </CardContent>
      </Card>
    </>
  );
}
