import { useMutation, useQuery } from '@tanstack/react-query';
import useDataService from '../../services/useDataService';
import { GET_DASHBOARD_DATA } from '../../constants/urls';
import { useAuthStore } from '@/store/authStore';

export const useGetDashboardData = (enabled = true) => {
    const { user } = useAuthStore();

    return useQuery<any, Error>({
        queryKey: ['dashboard-data', user?.user_id, user?.tenant_id],
        queryFn: async () => {
            const endpoint = `${GET_DASHBOARD_DATA}`;
            return await useDataService.getService(endpoint);
        },
        enabled: enabled && !!user?.user_id,
        staleTime: 0,
        gcTime: 0, // Don't cache data after component unmounts
    });
};